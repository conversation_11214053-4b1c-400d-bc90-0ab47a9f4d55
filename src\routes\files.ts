// filepath: c:\Users\<USER>\AndroidStudioProjects\Natural Order\src\routes\files.ts
import { Router, Request, Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { RequestHandler } from '../types/express-route-types.js';

const router = Router();

/**
 * 獲取指定目錄中的文件列表
 */
router.get('/list-files', (async (req, res) => {
  try {
    const dirPath = req.query.path ? String(req.query.path) : '.';
    const fullPath = path.resolve(process.cwd(), dirPath);
    
    // 安全檢查：確保路徑在允許的範圍內
    if (!fullPath.startsWith(process.cwd())) {
      return res.status(403).json({
        success: false,
        error: '禁止訪問該目錄'
      });
    }
    
    if (!fs.existsSync(fullPath)) {
      return res.status(404).json({
        success: false,
        error: '找不到指定的目錄'
      });
    }
    
    const files = fs.readdirSync(fullPath);
    
    res.json({
      success: true,
      path: dirPath,
      files
    });
  } catch (error) {
    console.error('列出文件時出錯:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
}) as RequestHandler);

/**
 * 獲取最新的 appPrompt 文件
 */
router.get('/get-latest-appprompt', (async (req, res) => {
  try {
    // 首先檢查 appPrompt 目錄
    const appPromptDir = path.join(process.cwd(), 'appPrompt');
    let appPromptFiles: string[] = [];
    
    if (fs.existsSync(appPromptDir)) {
      appPromptFiles = fs.readdirSync(appPromptDir)
        .filter(file => file.startsWith('appPrompt_') && file.endsWith('.json'));
    }
    
    // 如果 appPrompt 目錄中沒有文件，查找根目錄
    if (appPromptFiles.length === 0) {
      appPromptFiles = fs.readdirSync(process.cwd())
        .filter(file => file.startsWith('appPrompt_') && file.endsWith('.json'));
    }
    
    // 按照文件名排序（假設文件名包含日期時間）
    appPromptFiles.sort().reverse();
    
    if (appPromptFiles.length === 0) {
      return res.status(404).json({
        success: false,
        error: '找不到 appPrompt 文件'
      });
    }
    
    // 檢查是否為 appPrompt 目錄中的文件，如果是，加上路徑前綴
    const latestAppPrompt = appPromptFiles[0];
    const filePath = fs.existsSync(path.join(appPromptDir, latestAppPrompt))
      ? path.join('appPrompt', latestAppPrompt)
      : latestAppPrompt;
    
    res.json({
      success: true,
      filePath
    });
  } catch (error) {
    console.error('獲取最新 appPrompt 文件時出錯:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
}) as RequestHandler);

export default router;
