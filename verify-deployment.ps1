# 部署驗證腳本
# 驗證 Docker 部署是否成功

Write-Host "=== 自然語言點餐系統 - 部署驗證 ===" -ForegroundColor Green

# 檢查 Docker 容器狀態
Write-Host "`n1. 檢查容器狀態..." -ForegroundColor Blue
$containerStatus = docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
Write-Host $containerStatus

# 檢查容器是否運行
$runningContainers = docker-compose ps -q
if ($runningContainers) {
    Write-Host "✅ 容器正在運行" -ForegroundColor Green
} else {
    Write-Host "❌ 沒有運行中的容器" -ForegroundColor Red
    exit 1
}

# 檢查端口是否可訪問
Write-Host "`n2. 檢查服務端口..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3005" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 端口 3005 可訪問" -ForegroundColor Green
    } else {
        Write-Host "❌ 端口 3005 回應異常: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 無法訪問端口 3005: $($_.Exception.Message)" -ForegroundColor Red
}

# 檢查會話測試頁面
Write-Host "`n3. 檢查會話測試頁面..." -ForegroundColor Blue
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3005/session-test.html" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 會話測試頁面可訪問" -ForegroundColor Green
    } else {
        Write-Host "❌ 會話測試頁面回應異常: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 無法訪問會話測試頁面: $($_.Exception.Message)" -ForegroundColor Red
}

# 檢查數據目錄
Write-Host "`n4. 檢查數據目錄..." -ForegroundColor Blue
if (Test-Path "uploads") {
    Write-Host "✅ uploads 目錄存在" -ForegroundColor Green
} else {
    Write-Host "❌ uploads 目錄不存在" -ForegroundColor Red
}

if (Test-Path "appPrompt") {
    Write-Host "✅ appPrompt 目錄存在" -ForegroundColor Green
} else {
    Write-Host "❌ appPrompt 目錄不存在" -ForegroundColor Red
}

# 檢查預設文件
Write-Host "`n5. 檢查預設文件..." -ForegroundColor Blue
$defaultFiles = @("BDD_TW.txt", "BDD_En.txt", "BDD_JP.txt", "AAprompt_TW.txt", "AAprompt_En.txt", "AAprompt_JP.txt")
foreach ($file in $defaultFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}

# 檢查日誌
Write-Host "`n6. 檢查最近的日誌..." -ForegroundColor Blue
Write-Host "最近 10 行日誌:" -ForegroundColor Yellow
docker-compose logs --tail=10 natural-order

Write-Host "`n=== 驗證完成 ===" -ForegroundColor Green
Write-Host "如果所有檢查都通過，您可以訪問以下地址：" -ForegroundColor Cyan
Write-Host "🌐 主頁面: http://localhost:3005" -ForegroundColor Cyan
Write-Host "🧪 會話測試: http://localhost:3005/session-test.html" -ForegroundColor Cyan
