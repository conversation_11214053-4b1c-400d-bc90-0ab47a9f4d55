/* Toast 訊息樣式 */
.toast-message {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  min-width: 280px;
  max-width: 400px;
  word-wrap: break-word;
  text-align: left;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.toast-message.success {
  background-color: #e8f5e9;
  color: #2E7D32;
  border-left: 5px solid #2E7D32;
}

.toast-message.error {
  background-color: #ffebee;
  color: #C62828;
  border-left: 5px solid #C62828;
}

.toast-message.info {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-left: 5px solid #0d47a1;
}

.toast-message.warning {
  background-color: #fff8e1;
  color: #f57f17;
  border-left: 5px solid #f57f17;
}

/* 動畫效果 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(30px);
  }
}

/* 數量控制按鈕和刪除按鈕樣式 */
.quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-quantity {
    width: 28px;
    height: 28px;
    padding: 0;
    border: 1px solid #ddd;
    background-color: #f8f8f8;
    color: #333;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.2s ease;
}

.btn-quantity:hover {
    background-color: #e0e0e0;
}

.btn-quantity.plus:hover {
    background-color: #d6f5d6;
    border-color: #4CAF50;
    color: #4CAF50;
}

.btn-quantity.minus:hover {
    background-color: #fff0f0;
    border-color: #f44336;
    color: #f44336;
}

.item-quantity {
    margin: 0 8px;
    min-width: 24px;
    text-align: center;
    font-weight: bold;
}

.btn-remove {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: transparent;
    border: 1px solid #ddd;
    color: #f44336;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-remove:hover {
    background-color: #ffebee;
    border-color: #f44336;
}

.action-cell {
    text-align: center;
}

.quantity-cell {
    text-align: center;
}

/* 訂單確認模態框和其他相關樣式 */
.preview-table {
    border-collapse: collapse;
    width: 100%;
    margin-top: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

.preview-table th, 
.preview-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.preview-table th {
    background-color: #f5f5f5;
    color: #333;
    font-weight: bold;
}

.preview-table tbody tr:hover {
    background-color: #f9f9f9;
}

.preview-table tfoot tr {
    background-color: #f5f5f5;
    font-weight: bold;
}

/* 新增模糊匹配標籤樣式 */
.badge.fuzzy-match {
    display: inline-block;
    padding: 2px 6px;
    margin-left: 8px;
    background-color: #FFC107;
    color: #333;
    border-radius: 3px;
    font-size: 0.7em;
    vertical-align: middle;
}

/* 英文名稱樣式 */
.item-name-en {
    color: #757575;
    font-size: 0.9em;
    margin-left: 4px;
}

/* 數量控制按鈕樣式 */
.quantity-control {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.btn-quantity {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
}

.btn-quantity:hover {
    background-color: #e0e0e0;
}

.item-quantity {
    padding: 0 10px;
    font-weight: bold;
}

/* 刪除按鈕樣式 */
.btn-remove {
    background-color: transparent;
    color: #f44336;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.btn-remove:hover {
    background-color: #ffebee;
}

.order-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
