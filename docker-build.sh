#!/bin/bash

# Docker 構建和部署腳本
# 適用於 Linux/macOS

set -e

echo "=== 自然語言點餐系統 Docker 部署腳本 ==="

# 檢查 Docker 是否安裝
if ! command -v docker &> /dev/null; then
    echo "錯誤: 未找到 Docker，請先安裝 Docker"
    exit 1
fi

# 檢查 Docker Compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "錯誤: 未找到 Docker Compose，請先安裝 Docker Compose"
    exit 1
fi

# 檢查 .env 文件是否存在
if [ ! -f ".env" ]; then
    echo "警告: 未找到 .env 文件，正在複製 .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "已創建 .env 文件，請編輯此文件並填入您的 Firebase 配置"
        echo "編輯完成後請重新運行此腳本"
        exit 0
    else
        echo "錯誤: 未找到 .env.example 文件"
        exit 1
    fi
fi

# 創建必要的目錄
echo "創建數據目錄..."
if [ ! -d "uploads" ]; then
    mkdir -p uploads
    echo "已創建 uploads 目錄"
fi

if [ ! -d "appPrompt" ]; then
    mkdir -p appPrompt
    echo "已創建 appPrompt 目錄"
fi

# 設定目錄權限（如果需要）
if [ "$(id -u)" = "0" ]; then
    chown -R 1001:1001 uploads appPrompt 2>/dev/null || true
fi
chmod -R 755 uploads appPrompt 2>/dev/null || true

# 顯示選項菜單
echo ""
echo "請選擇操作:"
echo "1. 構建並啟動服務"
echo "2. 快速重新構建 (清理舊映像後重新構建)"
echo "3. 啟動服務 (docker-compose up -d)"
echo "4. 停止服務 (docker-compose down)"
echo "5. 查看服務狀態 (docker-compose ps)"
echo "6. 查看日誌 (docker-compose logs -f)"
echo "7. 重啟服務 (docker-compose restart)"
echo "8. 清理所有容器和映像 (docker-compose down --rmi all)"
echo "9. 退出"

read -p "請輸入選項 (1-9): " choice

case $choice in
    1)
        echo "正在構建並啟動服務..."
        docker-compose up -d --build
        if [ $? -eq 0 ]; then
            echo "服務已成功啟動!"
            echo "訪問地址: http://localhost:3005"
        else
            echo "服務啟動失敗，請檢查日誌"
            exit 1
        fi
        ;;
    2)
        echo "=== 快速重新構建模式 ==="
        echo "1. 停止現有容器..."
        docker-compose down

        echo "2. 清理舊映像..."
        docker rmi natural-order_natural-order 2>/dev/null || echo "舊映像不存在，跳過清理"

        echo "3. 重新構建映像..."
        docker-compose build --no-cache

        echo "4. 啟動服務..."
        docker-compose up -d

        echo "5. 檢查服務狀態..."
        sleep 5
        docker-compose ps

        echo ""
        echo "✅ 重新構建完成！"
        echo "🌐 訪問地址: http://localhost:3005"
        echo "📋 會話測試頁面: http://localhost:3005/session-test.html"
        ;;
    3)
        echo "正在啟動服務..."
        docker-compose up -d
        if [ $? -eq 0 ]; then
            echo "服務已成功啟動!"
            echo "訪問地址: http://localhost:3005"
        else
            echo "服務啟動失敗，請檢查日誌"
            exit 1
        fi
        ;;
    4)
        echo "正在停止服務..."
        docker-compose down
        echo "服務已停止"
        ;;
    5)
        echo "服務狀態:"
        docker-compose ps
        ;;
    6)
        echo "顯示日誌 (按 Ctrl+C 退出):"
        docker-compose logs -f
        ;;
    7)
        echo "正在重啟服務..."
        docker-compose restart
        echo "服務已重啟"
        ;;
    8)
        read -p "確定要清理所有容器和映像嗎? (y/N): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            echo "正在清理..."
            docker-compose down --rmi all
            echo "清理完成"
        else
            echo "已取消清理操作"
        fi
        ;;
    9)
        echo "退出腳本"
        exit 0
        ;;
    *)
        echo "無效選項，請重新運行腳本"
        exit 1
        ;;
esac

echo ""
echo "腳本執行完成"