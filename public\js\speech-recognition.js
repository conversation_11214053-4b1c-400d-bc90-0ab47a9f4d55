/**
 * 語音識別模組的前端整合
 * 提供語音控制和語音轉文字功能
 */
 
// 語音識別狀態
// 檢查全局變數，避免重複宣告
if (typeof window.speechRecognitionInstance === 'undefined') {
  window.speechRecognitionInstance = null;
}
let isRecognizing = false;

// DOM 元素
let micButton = null;
let statusIndicator = null;
let resultContainer = null;
let orderInput = null;

// 初始化語音識別功能
function initSpeechRecognition() {
  // 檢查瀏覽器支持
  if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
    console.error('您的瀏覽器不支援語音識別功能');
    disableSpeechFeatures();
    return;
  }
    // 建立語音識別物件
  window.speechRecognitionInstance = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
  
  // 配置語音識別 - 根據當前語言設定調整
  const currentLanguage = getCurrentLanguage();
  if (currentLanguage === 'en-US') {
    window.speechRecognitionInstance.lang = 'en-US';
  } else if (currentLanguage === 'ja-JP') {
    window.speechRecognitionInstance.lang = 'ja-JP';
  } else {
    window.speechRecognitionInstance.lang = 'zh-TW';
  }
  window.speechRecognitionInstance.continuous = false;
  window.speechRecognitionInstance.interimResults = true;
  window.speechRecognitionInstance.maxAlternatives = 1;
  
  // 設置事件處理
  setupRecognitionEvents();
  
  // 綁定 UI 元素
  bindUIElements();
}

// 配置語音識別事件
function setupRecognitionEvents() {
  if (!window.speechRecognitionInstance) return;
  
  window.speechRecognitionInstance.onstart = function() {
    isRecognizing = true;
    updateUI();
  };
  
  window.speechRecognitionInstance.onresult = function(event) {
    const result = event.results[event.resultIndex];
    const transcript = result[0].transcript;
    
    // 顯示中間結果
    if (!result.isFinal) {
      showInterimResult(transcript);
    } else {
      // 最終結果
      finalizeResult(transcript);
    }
  };
  
  window.speechRecognitionInstance.onerror = function(event) {
    console.error('語音識別錯誤:', event.error);
    showError(`語音識別錯誤: ${event.error}`);
    stopRecognition();
  };
  
  window.speechRecognitionInstance.onend = function() {
    isRecognizing = false;
    updateUI();
  };
}

// 綁定 UI 元素
function bindUIElements() {
  micButton = document.getElementById('mic-button');
  statusIndicator = document.getElementById('speech-status');
  resultContainer = document.getElementById('speech-result');
  orderInput = document.getElementById('order-input');
  
  if (micButton) {
    micButton.addEventListener('click', toggleRecognition);
  }
}

// 切換語音識別狀態
function toggleRecognition() {
  if (isRecognizing) {
    stopRecognition();
  } else {
    startRecognition();
  }
}

// 開始語音識別
function startRecognition() {
  if (!window.speechRecognitionInstance) return;
  
  try {
    window.speechRecognitionInstance.start();
    showStatus('正在聆聽...');
  } catch (error) {
    console.error('啟動語音識別失敗:', error);
    showError(`無法啟動語音識別: ${error.message}`);
  }
}

// 停止語音識別
function stopRecognition() {
  if (!window.speechRecognitionInstance) return;
  
  try {
    window.speechRecognitionInstance.stop();
    showStatus('已停止聆聽');
  } catch (error) {
    console.error('停止語音識別失敗:', error);
  }
}

// 顯示中間結果
function showInterimResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="interim-result">${text}</span>`;
  }
}

// 處理最終結果
function finalizeResult(text) {
  if (resultContainer) {
    resultContainer.innerHTML = `<span class="final-result">${text}</span>`;
  }
  
  // 如果有訂單輸入框，將識別結果填入
  if (orderInput) {
    orderInput.value = text;
    
    // 觸發輸入事件，以便其他依賴輸入的功能可以響應
    const inputEvent = new Event('input', { bubbles: true });
    orderInput.dispatchEvent(inputEvent);
  }
  
  // 自動提交訂單（可選功能，根據需要啟用或禁用）
  // autoSubmitOrder();
}

// 顯示語音識別狀態
function showStatus(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
  }
}

// 顯示錯誤信息
function showError(message) {
  if (statusIndicator) {
    statusIndicator.textContent = message;
    statusIndicator.classList.add('error');
    
    // 3 秒後清除錯誤狀態
    setTimeout(() => {
      statusIndicator.classList.remove('error');
    }, 3000);
  }
}

// 更新 UI 以反映當前狀態
function updateUI() {
  if (micButton) {
    if (isRecognizing) {
      micButton.classList.add('recording');
      micButton.innerHTML = '<i class="fas fa-stop"></i>';
      micButton.title = '點擊停止語音識別';
    } else {
      micButton.classList.remove('recording');
      micButton.innerHTML = '<i class="fas fa-microphone"></i>';
      micButton.title = '點擊開始語音識別';
    }
  }
}

// 自動提交訂單
function autoSubmitOrder() {
  const orderForm = document.getElementById('order-form');
  
  if (orderForm && orderInput && orderInput.value.trim()) {
    // 延遲提交以便用戶有時間檢查識別結果
    setTimeout(() => {
      orderForm.submit();
    }, 1500);
  }
}

// 禁用語音功能
function disableSpeechFeatures() {
  const speechElements = document.querySelectorAll('.speech-feature');
  speechElements.forEach(element => {
    element.classList.add('disabled');
    element.title = '您的瀏覽器不支援語音識別功能';
    
    // 禁用按钮
    if (element.tagName === 'BUTTON') {
      element.disabled = true;
    }
  });
  
  showStatus('語音識別不可用 - 瀏覽器不支援');
}

// 在頁面加載完成後初始化語音識別
document.addEventListener('DOMContentLoaded', function() {
  // 初始化語音識別功能
  initSpeechRecognition();
});

// 導出函數供其他腳本使用
window.voiceRecognition = {
  start: startRecognition,
  stop: stopRecognition,
  toggle: toggleRecognition
};
