/**
 * Firebase 配置檔案
 * 用於初始化 Firebase 連接和導出 Firestore 實例
 */

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, collection, doc, addDoc, getDoc,
  setDoc, updateDoc, deleteDoc, query, where, 
  getDocs, Timestamp, DocumentData 
} from 'firebase/firestore';
import dotenv from 'dotenv';

// 確保環境變數已載入
dotenv.config();

// Firebase 配置
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN || `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET || `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.FIREBASE_APP_ID || ''
};

// 初始化 Firebase
const app = initializeApp(firebaseConfig);

// 導出 Firestore 數據庫實例
export const db = getFirestore(app);

// 匯出常用的 Firestore 函數，方便使用
export {
  collection,
  doc,
  addDoc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  Timestamp,
  DocumentData
};

export default {
  db,
  firebaseConfig
};