{"prompt": "你是一個自然語言點餐系統，根據顧客的語音或文字輸入，識別餐點、查詢菜單數據、確認訂單並處理取消訂單等操作。", "parameters": {"feature": "自然語言點餐", "scenario": "顧客取消訂單", "given": ["顧客在點餐頁面上", "系統已顯示識別出的項目以供確認", "系統已顯示最終訂單以供確認"], "when": ["顧客在自然語言輸入框中輸入\"我想點一個大麥克套餐和一杯玉米湯\"", "顧客使用語音輸入說\"我要一個麥脆雞兩塊餐\"", "顧客說\"玉米湯換成可樂\"", "顧客輸入\"來一份神秘魔法漢堡\"", "顧客說\"再加一份薯條\"", "顧客說\"玉米湯不要了\"", "顧客輸入\"我要一個吉士漢堡和一個不知道什麼派\"", "顧客輸入\"大麥克是什麼\"", "顧客說\"確認\"或\"沒問題\"", "顧客說\"取消\"或\"我不要了\""], "then": ["系統應識別\"大麥克套餐\"和\"玉米湯\"", "查詢數據庫/RAG 以獲取\"大麥克套餐\"和\"玉米湯\"的詳細信息（名稱、價格、圖片、供應情況）", "使用自然語言向顧客顯示已識別的項目及其詳細信息以供確認（例如：\"您是想點一份大麥克套餐和一份玉米湯嗎？\")", "系統應將語音輸入轉錄為文本", "識別\"麥脆雞兩塊餐\"", "查詢數據庫/RAG 以獲取\"麥脆雞兩塊餐\"的詳細信息（名稱、價格、圖片、供應情況）", "使用自然語言向顧客顯示已識別的項目及其詳細信息以供確認（例如：\"您選的是一份麥脆雞兩塊餐，對嗎？\")", "系統應從建議的訂單中移除\"玉米湯\"", "查詢數據庫/RAG 以獲取\"可樂\"的詳細信息", "使用自然語言向顧客顯示修改後的訂單（大麥克套餐，可樂）以供確認", "系統應指示未找到項目\"神秘魔法漢堡\"", "建議檢查菜單或重新措辭請求", "系統應識別\"薯條\"", "查詢數據庫/RAG 以獲取\"薯條\"的詳細信息", "將\"薯條\"添加到建議的訂單中", "使用自然語言向顧客顯示更新後的訂單（大麥克套餐，薯條）以供確認（例如：\"好的，幫您加了一份薯條，現在是包含大麥克套餐和薯條，請問確認嗎？\")", "系統應從建議的訂單中移除\"玉米湯\"", "使用自然語言向顧客顯示更新後的訂單（大麥克套餐）以供確認（例如：\"好的，已經移除玉米湯，目前是只有大麥克套餐，請問確認嗎？\")", "系統應識別\"吉士漢堡\"", "指示未找到項目\"不知道什麼派\"", "建議檢查菜單或重新措辭對未知項目的請求", "系統應查詢數據庫/RAG 以獲取\"大麥克\"的詳細信息（描述、成分等）", "使用自然語言向顧客顯示有關\"大麥克\"的信息（例如：\"大麥克是一款經典的雙層牛肉漢堡，包含...\"）", "系統應處理訂單", "顯示訂單確認消息（例如：\"您的訂單已送出！\"）", "系統應取消當前訂單", "顯示一條消息，指示取消（例如：\"好的，您的訂單已取消。\"）"], "menu": [{"category": "套餐", "items": [{"id": "11", "name_zh": "四盎司牛肉堡套餐", "price": 157}, {"id": "14", "name_zh": "培根生菜番茄烤雞堡套餐", "price": 187}, {"id": "1", "name_zh": "大麥克套餐", "price": 143}, {"id": "13", "name_zh": "安格斯培根生菜番茄牛肉堡套餐", "price": 187}, {"id": "15", "name_zh": "安格斯蘑菇牛肉堡套餐", "price": 197}, {"id": "18", "name_zh": "帕瑪森主廚雞堡套餐", "price": 192}, {"id": "17", "name_zh": "帕瑪森安格斯牛肉堡套餐", "price": 192}, {"id": "3", "name_zh": "烤雞堡套餐", "price": 148}, {"id": "16", "name_zh": "蘑菇主廚雞堡套餐", "price": 197}, {"id": "2", "name_zh": "雙層吉士漢堡套餐", "price": 137}, {"id": "12", "name_zh": "雙層四盎司牛肉堡套餐", "price": 197}, {"id": "9", "name_zh": "雙層麥香雞套餐", "price": 143}, {"id": "4", "name_zh": "麥香雞套餐", "price": 113}, {"id": "7", "name_zh": "麥香雞漢堡套餐", "price": 143}, {"id": "10", "name_zh": "麥香魚套餐", "price": 117}]}, {"category": "炸雞類", "items": [{"id": "6", "name_zh": "麥克雞塊(10塊)套餐", "price": 174}, {"id": "5", "name_zh": "麥克雞塊(6塊)套餐", "price": 133}, {"id": "8", "name_zh": "麥脆雞(2塊)套餐", "price": 191}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-04T07:49:19.670Z", "aiGenerated": true}}