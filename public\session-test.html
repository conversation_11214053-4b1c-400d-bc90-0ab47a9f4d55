<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>會話隔離測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .session-panel {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        
        .session-panel h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .session-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #218838;
        }
        
        .test-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active {
            background-color: #28a745;
        }
        
        .status-inactive {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>🧪 會話隔離測試頁面</h1>
    <p>此頁面用於測試多用戶會話隔離功能。您可以模擬多個用戶同時使用系統的情況。</p>
    
    <div class="test-container">
        <h2>測試說明</h2>
        <ol>
            <li>每個會話面板代表一個獨立的用戶會話</li>
            <li>每個會話都有自己的 sessionId、APPprompt 和菜單數據</li>
            <li>測試不同會話之間的數據隔離</li>
            <li>驗證點餐請求使用正確的 APPprompt</li>
        </ol>
    </div>
    
    <!-- 會話 A -->
    <div class="session-panel">
        <h3>👤 用戶 A - 麥當勞日文菜單</h3>
        <div class="session-info" id="session-a-info">
            會話ID: 未初始化
        </div>
        
        <div>
            <button class="btn" onclick="initSession('A')">初始化會話</button>
            <button class="btn btn-success" onclick="setMockMenu('A', 'mcdonalds', 'ja')">設置麥當勞日文菜單</button>
            <button class="btn btn-success" onclick="generateMockPrompt('A')">生成 APPprompt</button>
            <button class="btn btn-danger" onclick="clearSession('A')">清除會話</button>
        </div>
        
        <div>
            <input type="text" class="test-input" id="order-a" placeholder="輸入點餐請求（日文）" value="ビッグマックセットをお願いします">
            <button class="btn" onclick="processOrder('A')">處理點餐</button>
        </div>
        
        <div class="test-result" id="result-a">等待操作...</div>
    </div>
    
    <!-- 會話 B -->
    <div class="session-panel">
        <h3>👤 用戶 B - KFC中文菜單</h3>
        <div class="session-info" id="session-b-info">
            會話ID: 未初始化
        </div>
        
        <div>
            <button class="btn" onclick="initSession('B')">初始化會話</button>
            <button class="btn btn-success" onclick="setMockMenu('B', 'kfc', 'zh')">設置KFC中文菜單</button>
            <button class="btn btn-success" onclick="generateMockPrompt('B')">生成 APPprompt</button>
            <button class="btn btn-danger" onclick="clearSession('B')">清除會話</button>
        </div>
        
        <div>
            <input type="text" class="test-input" id="order-b" placeholder="輸入點餐請求（中文）" value="我要一個上校雞塊套餐">
            <button class="btn" onclick="processOrder('B')">處理點餐</button>
        </div>
        
        <div class="test-result" id="result-b">等待操作...</div>
    </div>
    
    <!-- 會話 C -->
    <div class="session-panel">
        <h3>👤 用戶 C - 星巴克英文菜單</h3>
        <div class="session-info" id="session-c-info">
            會話ID: 未初始化
        </div>
        
        <div>
            <button class="btn" onclick="initSession('C')">初始化會話</button>
            <button class="btn btn-success" onclick="setMockMenu('C', 'starbucks', 'en')">設置星巴克英文菜單</button>
            <button class="btn btn-success" onclick="generateMockPrompt('C')">生成 APPprompt</button>
            <button class="btn btn-danger" onclick="clearSession('C')">清除會話</button>
        </div>
        
        <div>
            <input type="text" class="test-input" id="order-c" placeholder="輸入點餐請求（英文）" value="I'd like a grande latte with extra shot">
            <button class="btn" onclick="processOrder('C')">處理點餐</button>
        </div>
        
        <div class="test-result" id="result-c">等待操作...</div>
    </div>
    
    <div class="test-container">
        <h2>🔄 批量測試</h2>
        <button class="btn" onclick="runBatchTest()">執行批量測試</button>
        <button class="btn btn-danger" onclick="clearAllSessions()">清除所有會話</button>
        <div class="test-result" id="batch-result">等待批量測試...</div>
    </div>

    <!-- 引入會話管理器 -->
    <script src="js/session-manager.js"></script>
    
    <script>
        // 存儲多個會話管理器實例
        const sessions = {};
        
        // 模擬菜單數據
        const mockMenus = {
            mcdonalds: {
                zh: { restaurant_name: "麥當勞", categories: [{ name_zh: "主餐", items: [{ name_zh: "大麥克套餐", price: 150 }] }] },
                ja: { restaurant_name: "マクドナルド", categories: [{ name_jp: "メイン", items: [{ name_jp: "ビッグマックセット", price: 500 }] }] },
                en: { restaurant_name: "McDonald's", categories: [{ name_en: "Main", items: [{ name_en: "Big Mac Meal", price: 8.99 }] }] }
            },
            kfc: {
                zh: { restaurant_name: "肯德基", categories: [{ name_zh: "套餐", items: [{ name_zh: "上校雞塊套餐", price: 180 }] }] },
                ja: { restaurant_name: "ケンタッキー", categories: [{ name_jp: "セット", items: [{ name_jp: "チキンナゲットセット", price: 600 }] }] },
                en: { restaurant_name: "KFC", categories: [{ name_en: "Meals", items: [{ name_en: "Chicken Nuggets Meal", price: 9.99 }] }] }
            },
            starbucks: {
                zh: { restaurant_name: "星巴克", categories: [{ name_zh: "咖啡", items: [{ name_zh: "拿鐵咖啡", price: 120 }] }] },
                ja: { restaurant_name: "スターバックス", categories: [{ name_jp: "コーヒー", items: [{ name_jp: "ラテ", price: 400 }] }] },
                en: { restaurant_name: "Starbucks", categories: [{ name_en: "Coffee", items: [{ name_en: "Latte", price: 4.50 }] }] }
            }
        };
        
        // 初始化會話
        function initSession(sessionKey) {
            // 創建新的會話管理器實例
            const SessionManager = window.SessionManager || class {
                constructor() {
                    this.sessionId = `test_${sessionKey}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                    this.appPrompt = null;
                    this.language = 'zh-TW';
                    this.menuData = null;
                }
                
                setAppPrompt(prompt) { this.appPrompt = prompt; }
                getAppPrompt() { return this.appPrompt; }
                setLanguage(lang) { this.language = lang; }
                getLanguage() { return this.language; }
                setMenuData(data) { this.menuData = data; }
                getMenuData() { return this.menuData; }
                
                getSessionStatus() {
                    return {
                        sessionId: this.sessionId,
                        hasAppPrompt: !!this.appPrompt,
                        language: this.language,
                        hasMenuData: !!this.menuData
                    };
                }
                
                clearSession() {
                    this.appPrompt = null;
                    this.menuData = null;
                    this.sessionId = `test_${sessionKey}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                }
                
                async processOrder(input) {
                    if (!this.appPrompt) {
                        throw new Error('沒有可用的 APPprompt');
                    }
                    
                    // 模擬 API 調用
                    const response = await fetch('/api/prompt/process-order', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            customerRequest: input,
                            language: this.language,
                            sessionId: this.sessionId,
                            appPrompt: this.appPrompt
                        })
                    });
                    
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error);
                    }
                    
                    return result.result;
                }
            };
            
            sessions[sessionKey] = new SessionManager();
            updateSessionInfo(sessionKey);
            updateResult(sessionKey, `會話 ${sessionKey} 已初始化`);
        }
        
        // 設置模擬菜單
        function setMockMenu(sessionKey, restaurant, language) {
            if (!sessions[sessionKey]) {
                updateResult(sessionKey, '請先初始化會話');
                return;
            }
            
            const menuData = mockMenus[restaurant][language];
            sessions[sessionKey].setMenuData(menuData);
            sessions[sessionKey].setLanguage(language);
            updateSessionInfo(sessionKey);
            updateResult(sessionKey, `已設置 ${restaurant} ${language} 菜單`);
        }
        
        // 生成模擬 APPprompt
        function generateMockPrompt(sessionKey) {
            if (!sessions[sessionKey]) {
                updateResult(sessionKey, '請先初始化會話');
                return;
            }
            
            const menuData = sessions[sessionKey].getMenuData();
            if (!menuData) {
                updateResult(sessionKey, '請先設置菜單');
                return;
            }
            
            const mockPrompt = JSON.stringify({
                prompt: `你是 ${menuData.restaurant_name} 的點餐助手`,
                parameters: {
                    restaurant: menuData.restaurant_name,
                    language: sessions[sessionKey].getLanguage(),
                    menu: menuData.categories
                }
            });
            
            sessions[sessionKey].setAppPrompt(mockPrompt);
            updateSessionInfo(sessionKey);
            updateResult(sessionKey, `已生成 ${menuData.restaurant_name} 的 APPprompt`);
        }
        
        // 處理點餐
        async function processOrder(sessionKey) {
            if (!sessions[sessionKey]) {
                updateResult(sessionKey, '請先初始化會話');
                return;
            }
            
            const orderInput = document.getElementById(`order-${sessionKey.toLowerCase()}`);
            const orderText = orderInput.value.trim();
            
            if (!orderText) {
                updateResult(sessionKey, '請輸入點餐內容');
                return;
            }
            
            try {
                updateResult(sessionKey, '處理中...');
                const result = await sessions[sessionKey].processOrder(orderText);
                updateResult(sessionKey, `點餐成功！\n回應: ${result}`);
            } catch (error) {
                updateResult(sessionKey, `點餐失敗: ${error.message}`);
            }
        }
        
        // 清除會話
        function clearSession(sessionKey) {
            if (sessions[sessionKey]) {
                sessions[sessionKey].clearSession();
                updateSessionInfo(sessionKey);
                updateResult(sessionKey, `會話 ${sessionKey} 已清除`);
            }
        }
        
        // 更新會話信息顯示
        function updateSessionInfo(sessionKey) {
            const infoElement = document.getElementById(`session-${sessionKey.toLowerCase()}-info`);
            if (sessions[sessionKey] && infoElement) {
                const status = sessions[sessionKey].getSessionStatus();
                infoElement.innerHTML = `
                    會話ID: ${status.sessionId}<br>
                    菜單: <span class="status-indicator ${status.hasMenuData ? 'status-active' : 'status-inactive'}"></span>${status.hasMenuData ? '已載入' : '未載入'}<br>
                    APPprompt: <span class="status-indicator ${status.hasAppPrompt ? 'status-active' : 'status-inactive'}"></span>${status.hasAppPrompt ? '已生成' : '未生成'}<br>
                    語言: ${status.language}
                `;
            }
        }
        
        // 更新結果顯示
        function updateResult(sessionKey, message) {
            const resultElement = document.getElementById(`result-${sessionKey.toLowerCase()}`);
            if (resultElement) {
                const timestamp = new Date().toLocaleTimeString();
                resultElement.textContent = `[${timestamp}] ${message}`;
            }
        }
        
        // 批量測試
        async function runBatchTest() {
            const batchResult = document.getElementById('batch-result');
            batchResult.textContent = '開始批量測試...\n';
            
            try {
                // 初始化所有會話
                ['A', 'B', 'C'].forEach(key => {
                    initSession(key);
                    batchResult.textContent += `✓ 會話 ${key} 初始化完成\n`;
                });
                
                // 設置不同的菜單
                setMockMenu('A', 'mcdonalds', 'ja');
                setMockMenu('B', 'kfc', 'zh');
                setMockMenu('C', 'starbucks', 'en');
                batchResult.textContent += '✓ 所有菜單設置完成\n';
                
                // 生成 APPprompt
                ['A', 'B', 'C'].forEach(key => {
                    generateMockPrompt(key);
                    batchResult.textContent += `✓ 會話 ${key} APPprompt 生成完成\n`;
                });
                
                // 並發處理點餐請求
                const orderPromises = [
                    sessions['A'].processOrder('ビッグマックセットをお願いします'),
                    sessions['B'].processOrder('我要一個上校雞塊套餐'),
                    sessions['C'].processOrder("I'd like a grande latte with extra shot")
                ];
                
                const results = await Promise.all(orderPromises);
                
                batchResult.textContent += '\n=== 點餐結果 ===\n';
                results.forEach((result, index) => {
                    const sessionKey = ['A', 'B', 'C'][index];
                    batchResult.textContent += `會話 ${sessionKey}: ${result.substring(0, 100)}...\n`;
                });
                
                batchResult.textContent += '\n✅ 批量測試完成！所有會話都能正確隔離運行。';
                
            } catch (error) {
                batchResult.textContent += `\n❌ 批量測試失敗: ${error.message}`;
            }
        }
        
        // 清除所有會話
        function clearAllSessions() {
            ['A', 'B', 'C'].forEach(key => {
                if (sessions[key]) {
                    clearSession(key);
                }
            });
            document.getElementById('batch-result').textContent = '所有會話已清除';
        }
        
        // 頁面載入時的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('會話隔離測試頁面已載入');
        });
    </script>
</body>
</html>
