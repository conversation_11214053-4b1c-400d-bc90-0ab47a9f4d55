// 這是一個修復路由問題的示例解決方案

// 1. 使用 npm 更新 Express 和 TypeScript 類型定義
// npm install --save express@latest @types/express@latest

// 2. 修改 nlp.ts 文件
import { Router, Request, Response } from 'express';
import { MenuProcessor } from '../services/MenuProcessor.js';
import { NLPService } from '../services/NLPService.js';
import { MenuItem } from '../types/menu.js';
import path from 'node:path';
import fs from 'node:fs';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = Router();
const nlpService = new NLPService();
const menuProcessor = new MenuProcessor();

// 臨時存儲加載的菜單數據
const loadedMenus: { [key: string]: MenuItem[] } = {};

// 初始加載默認菜單
loadDefaultMenus();

// 定義處理器函數
// 自然語言處理 API
const processHandler = async (req: Request, res: Response) => {
  try {
    const { text, input, context } = req.body;
    
    // 支援兩種輸入格式：text (直接文本) 和 input (舊版相容)
    const userInput = text || input;
    
    if (!userInput) {
      return res.status(400).json({
        success: false,
        error: '請提供輸入文本'
      });
    }
    
    // 獲取相關的菜單，默認使用麥當勞菜單
    const restaurantId = context?.restaurant_id || 'mcdonalds';
    const menuItems = loadedMenus[restaurantId] || [];
    
    if (menuItems.length === 0) {
      return res.status(400).json({
        success: false,
        error: '找不到相關的菜單資料'
      });
    }
      // 使用簡化的 Gemini AI 處理
    const result = await nlpService.processOrderWithAppPromptFirst(userInput);
    
    res.json({
      success: result.success,
      data: result.data,
      processed: {
        entities: result.analysis?.entities || []
      },
      analysis: result.analysis,
      error: result.error
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `處理文本時發生錯誤: ${error.message}`
    });
  }
};

// 處理訂單修改
const modifyHandler = async (req: Request, res: Response) => {
  try {
    const { input, currentItems, context } = req.body;
    
    if (!input || !currentItems || !Array.isArray(currentItems)) {
      return res.status(400).json({
        success: false,
        error: '請提供輸入文本和當前餐點列表'
      });
    }
    
    // 獲取相關的菜單
    const restaurantId = context?.restaurant_id || 'default_restaurant';
    const menuItems = loadedMenus[restaurantId] || [];
    
    if (menuItems.length === 0) {
      return res.status(400).json({
        success: false,
        error: '找不到相關的菜單資料'
      });
    }
      // 簡化的修改處理：將修改請求當作新訂單處理
    const result = await nlpService.processOrderWithAppPromptFirst(input);
    
    res.json({
      success: true,
      result: {
        action: "new_order",
        message: "修改請求已作為新訂單處理",
        aiResponse: result.data?.aiResponse,
        originalItems: currentItems
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `處理修改指令時發生錯誤: ${error.message}`
    });
  }
};

// 獲取指定餐廳的菜單
const getMenuHandler = (req: Request, res: Response) => {
  const { restaurantId } = req.params;
  
  if (!loadedMenus[restaurantId]) {
    return res.status(404).json({
      success: false,
      error: '找不到指定餐廳的菜單'
    });
  }
  
  res.json({
    success: true,
    menu: loadedMenus[restaurantId]
  });
};

// 註冊路由
router.post('/process', function(req, res) {
  processHandler(req, res);
});
router.post('/modify', function(req, res) {
  modifyHandler(req, res);
});
router.get('/menu/:restaurantId', function(req, res) {
  getMenuHandler(req, res);
});

// 載入默認菜單
function loadDefaultMenus() {
  try {
    // 載入麥當勞菜單
    const mcdonaldsMenu: MenuItem[] = [];
    
    // 讀取漢堡菜單
    const burgersPath = path.join(__dirname, '../../document/Mcdonalds_menu/burgers.csv');
    if (fs.existsSync(burgersPath)) {
      const result = menuProcessor.processMenuFile(
        burgersPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      result.then(processedMenu => {
        if (processedMenu.success && processedMenu.data) {
          processedMenu.data.categories.forEach(category => {
            mcdonaldsMenu.push(...category.items);
          });
        }
      });
    }
    
    // 讀取飲料菜單
    const drinksPath = path.join(__dirname, '../../document/Mcdonalds_menu/drinks.csv');
    if (fs.existsSync(drinksPath)) {
      const result = menuProcessor.processMenuFile(
        drinksPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      result.then(processedMenu => {
        if (processedMenu.success && processedMenu.data) {
          processedMenu.data.categories.forEach(category => {
            mcdonaldsMenu.push(...category.items);
          });
        }
      });
    }
    
    // 讀取套餐菜單
    const mealsPath = path.join(__dirname, '../../document/Mcdonalds_menu/extravaluemeal.csv');
    if (fs.existsSync(mealsPath)) {
      const result = menuProcessor.processMenuFile(
        mealsPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      result.then(processedMenu => {
        if (processedMenu.success && processedMenu.data) {
          processedMenu.data.categories.forEach(category => {
            mcdonaldsMenu.push(...category.items);
          });
        }
      });
    }
    
    // 讀取配菜菜單
    const sidesPath = path.join(__dirname, '../../document/Mcdonalds_menu/sides.csv');
    if (fs.existsSync(sidesPath)) {
      const result = menuProcessor.processMenuFile(
        sidesPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      result.then(processedMenu => {
        if (processedMenu.success && processedMenu.data) {
          processedMenu.data.categories.forEach(category => {
            mcdonaldsMenu.push(...category.items);
          });
        }
      });
    }
    
    // 將麥當勞菜單加入到已載入的菜單中
    loadedMenus['mcdonalds'] = mcdonaldsMenu;
    
    console.log('默認菜單載入完成');
  } catch (error: any) {
    console.error('載入默認菜單時發生錯誤:', error.message);
  }
}

export default router;
