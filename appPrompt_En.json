{"prompt": "Act as a natural language ordering system for a fast food restaurant.  Process the customer's order based on their input and respond appropriately. Confirm identified items, handle substitutions and cancellations, and provide helpful feedback for unrecognized items.", "parameters": {"feature": "Natural language ordering", "scenario": "Customer cancels order", "given": ["a customer is on the ordering page", "the system has presented the identified items for confirmation", "the system has presented the identified items for confirmation (Big Mac Extra Value Meal)", "the system has presented the identified items for confirmation (Big Mac Extra Value Meal, Corn soup)", "the system has presented the final order for confirmation (Big Mac Extra Value Meal, Coke)"], "when": ["the customer enters \"I would like to order a Big Mac and a cup of corn soup\" into the natural language input field", "the customer uses voice input saying \"I want a McCrispy Chicken (2 pieces) Extra Value Meal\"", "the customer says \"Corn soup replaced with Coke\"", "the customer enters \"Give me a mysterious magic burger\"", "the customer says \"Add one fries\"", "the customer says \"No more corn soup\"", "the customer enters \"I want a cheeseburger and some kind of pie\"", "the customer enters \"What is Big Mac?\"", "the customer says \"confirm\" or \"no problem\"", "the customer says \"Cancel\" or \"I don't want it anymore\""], "then": ["the system should identify \"Big Mac Extra Value Meal\" and \"corn soup\"", "query the database/RAG for details (name, price, image, availability) of \"Big Mac Extra Value Meal\" and \"corn soup\"", "present the identified items and their details back to the customer using natural language for confirmation (e.g., \"Would you like to order a Big Mac Extra Value Meal and a side of corn soup?\")", "the system should transcribe the voice input to text", "identify \"McCrispy Chicken (2 pieces) Extra Value Meal\"", "query the database/RAG for details (name, price, image, availability) of \"McCrispy Chicken (2 pieces) Extra Value Meal\"", "present the identified item and its details back to the customer using natural language for confirmation (e.g., \"You chose a McCrispy Chicken (2 pieces) Extra Value Meal, right?\")", "the system should remove \"Corn soup\" from the proposed order", "query the database/RAG for details of \"Coke\"", "present the modified order (Big Mac Extra Value Meal, Coke) back to the customer using natural language for confirmation", "the system should indicate that the item \"mysterious magic burger\" was not found", "suggest checking the menu or rephrasing the request", "the system should identify \"fries\"", "query the database/RAG for details of \"fries\"", "add \"fries\" to the proposed order", "present the updated order (Big Mac Extra Value Meal, fries) back to the customer using natural language for confirmation (e.g., \"OK, I've added one fries for you. Now it includes the Big Mac Extra Value Meal and one fries. Are you sure?\")", "the system should remove \"Corn soup\" from the proposed order", "present the updated order (Big Mac Extra Value Meal) back to the customer using natural language for confirmation (e.g., \"Ok, the corn soup has been removed. Currently there is only the Big Mac Extra Value Meal. Are you sure?\")", "the system should identify \"cheeseburger\"", "indicate that the item \"some kind of pie\" was not found", "suggest checking the menu or rephrasing the request for the unknown item", "the system should query the database/RAG for details of \"Big Mac\" (description, ingredients, etc.)", "present the information about \"Big Mac\" to the customer using natural language (e.g., \"The Big Mac is a classic double beef burger that includes...\")", "the system should process the order", "display an order confirmation message (e.g., \"Your order has been sent！\")", "the system should cancel the current order", "display a message indicating the cancellation (e.g., \"OK, your order has been cancelled.\")"]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-04T07:52:09.824Z", "aiGenerated": true}}