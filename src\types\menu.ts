// 菜單數據類型定義

// 菜單項目
export interface MenuItem {
  id: string;
  name_en?: string;
  name_zh: string;
  name_jp?: string;
  price: number;
  price_jp?: number;
  category?: string;
  description?: string;
  image_url?: string;
  availability?: boolean;
  // 飲料特有屬性
  size_en?: string;
  size_zh?: string;
  size_jp?: string;
  // 套餐特有屬性
  main_item?: string;
  side_item?: string;
  drink_item?: string;
}

// 菜單分類
export interface MenuCategory {
  id: string;
  name_zh: string;
  name_en?: string;
  name_jp?: string;
  items: MenuItem[];
}

// 完整菜單數據
export interface MenuData {
  restaurant_id: string;
  restaurant_name: string;
  categories: MenuCategory[];
  last_updated: Date;
  version: string;
}

// 菜單上傳結果
export interface MenuUploadResult {
  success: boolean;
  data?: MenuData;
  errors?: string[];
  warnings?: string[];
}

// 菜單驗證規則
export interface MenuValidationRule {
  field: string;
  required: boolean;
  type: 'string' | 'number' | 'boolean';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
}

// 菜單處理選項
export interface MenuProcessingOptions {
  skipValidation?: boolean;
  autoDetectCategory?: boolean;
  mergeDuplicates?: boolean;
  language?: 'zh' | 'en' | 'both';
}

// 菜單上傳檔案格式
export interface MenuUploadFormat {
  fileTypes: string[];
  maxFileSize: string;
  requiredColumns: string[];
  optionalColumns: string[];
}

// 菜單搜尋上下文
export interface MenuContext {
  restaurant_id: string;
  language?: 'zh' | 'en' | 'both';
  category?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  availableOnly?: boolean;
}

// 菜單項目識別結果
export interface MenuItemIdentificationResult {
  input: string;
  matches: Array<{
    item: MenuItem;
    confidence: number;
    quantity: number;
    strictMatch?: boolean;   // 是否為嚴格匹配
    isFuzzyMatch?: boolean;  // 是否為模糊匹配
  }>;
  unidentified: string[];
}

// 處理過的自然語言輸入
export interface ProcessedNLPInput {
  originalText: string;
  normalizedText: string;
  tokens: string[];
  entities: {
    menuItems: string[];
    quantities: { value: number, unit?: string }[];
    modifiers: string[];
    dates?: string[];
  };
}

// BDD 格式規範
export interface BDDSpec {
  feature: string;
  scenario: string;
  given: string[];
  when: string[];
  then: string[];
}

// AAprompt 格式規範
export interface AAPrompt {
  actor: string;
  action: string;
  context?: string;
  constraints?: string[];
}

// APPprompt 生成結果
export interface APPPromptResult {
  prompt: string;
  parameters: Record<string, any>;
  metadata: {
    source: 'bdd' | 'aaprompt' | 'natural';
    generatedAt: Date;
    aiGenerated?: boolean;
  };
  filePath?: string; // 可選的文件路徑屬性，用於指示 APPprompt 文件的保存位置
}
