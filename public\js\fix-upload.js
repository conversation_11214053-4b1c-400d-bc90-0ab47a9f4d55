/**
 * 修復檔案上傳問題的腳本
 * 這個腳本將確保檔案選擇對話框能夠正確開啟
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[修復腳本] 正在修復檔案選擇對話框問題...');
    
    // 立即嘗試修復
    applyFix();
    
    // 再次嘗試修復（有些元素可能會延遲加載）
    setTimeout(applyFix, 1000);
    setTimeout(applyFix, 2000);
});

// 應用修復函數
function applyFix() {
    console.log('[修復腳本] 應用修復...');
    
    // 檢查是否已經進行過修復
    if (window.fixUploadApplied) {
        console.log('[修復腳本] 修復已經應用過，跳過');
        return;
    }
    
    // 1. 找到元素
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const uploadBtn = document.getElementById('upload-btn');
    const browseBtn = document.getElementById('browse-btn');
    
    // 檢查元素是否存在
    console.log('[修復腳本] 檢查元素:', {
        '拖放區域存在': !!dropArea,
        '文件輸入存在': !!fileInput,
        '上傳按鈕存在': !!uploadBtn,
        '選擇檔案按鈕存在': !!browseBtn
    });
    
    // 標記為已應用修復
    window.fixUploadApplied = true;
      // 2. 修復文件輸入顯示問題
    if (fileInput) {
        // 確保文件輸入可見且可以接收點擊事件
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.zIndex = '2';  // 提高z-index
        fileInput.style.width = '100%';
        fileInput.style.height = '100%';
        fileInput.style.top = '0';
        fileInput.style.left = '0';
        fileInput.style.cursor = 'pointer';
    }
      // 3. 確保拖放區域的點擊事件能觸發檔案選擇
    if (dropArea) {
        console.log('[修復腳本] 確保拖放區域正確處理點擊事件');
        
        // 確保瀏覽按鈕也能觸發檔案選擇
        const browseButton = document.getElementById('browse-btn');
        if (browseButton) {
            browseButton.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('[修復腳本] 點擊瀏覽按鈕');
                
                if (fileInput) {
                    try {
                        fileInput.click();
                    } catch (err) {
                        console.error('[修復腳本] 瀏覽按鈕點擊失敗:', err);
                        createTemporaryFileInput();
                    }
                } else {
                    createTemporaryFileInput();
                }
            };
        }
        
        // 修改拖放區域的點擊事件，但允許事件冒泡，以便點擊文件輸入
        dropArea.addEventListener('click', function(e) {
            console.log('[修復腳本] 點擊拖放區域');
            // 注意：不阻止默認行為，允許事件冒泡到file input
        }, false);
        
        // 文件選擇變更事件
        if (fileInput && !fileInput.dataset.fixApplied) {
            fileInput.dataset.fixApplied = 'true';
            
            fileInput.onchange = function(e) {
                console.log('[修復腳本] 檔案選擇事件觸發');
                if (e.target && e.target.files && e.target.files.length > 0) {
                    const file = e.target.files[0];
                    // 更新顯示
                    const selectedFileText = getTranslation('file_selected') || '已選擇檔案';
                    const fileSizeText = getTranslation('file_size') || '檔案大小';
                    dropArea.innerHTML = `<p>${selectedFileText}：${file.name}</p>
                                         <p class="small">${fileSizeText}：${formatFileSize(file.size)}</p>`;
                    
                    // 啟用上傳按鈕
                    if (uploadBtn) {
                        uploadBtn.disabled = false;
                    }
                    
                    // 顯示成功消息
                    const fileSelectedSuccessMsg = getTranslation('file_selected_success') || '檔案已成功選擇，請點擊「上傳菜單」按鈕完成上傳';
                    showMessage(fileSelectedSuccessMsg, 'success');
                }
            };
        }
    } else {
        console.error('[修復腳本] 找不到拖放區域');
    }
}

// 創建臨時文件輸入 (對於不支援或有問題的瀏覽器)
function createTemporaryFileInput() {
    console.log('[修復腳本] 創建臨時檔案輸入框');
    
    // 創建臨時文件輸入框
    const tempInput = document.createElement('input');
    tempInput.type = 'file';
    tempInput.accept = '.csv,.xlsx,.json';
    document.body.appendChild(tempInput);
    
    // 點擊文件選擇
    try {
        tempInput.click();
    } catch (err) {
        console.error('[修復腳本] 臨時檔案輸入點擊失敗:', err);
        showMessage(getTranslation('cannot_open_file_dialog') || '無法開啟檔案選擇對話框，請嘗試點擊「選擇檔案」按鈕', 'error');
        document.body.removeChild(tempInput);
        return;
    }
    
    // 監聽變更事件
    tempInput.onchange = function(e) {
        if (e.target && e.target.files && e.target.files.length > 0) {
            const file = e.target.files[0];
            const originalInput = document.getElementById('file-input');
            
            // 如果找到原始輸入框，則設置其文件
            if (originalInput) {
                // 嘗試設置檔案
                try {
                    // 現代瀏覽器支援 DataTransfer API
                    if (typeof DataTransfer !== 'undefined') {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        originalInput.files = dataTransfer.files;
                    } else {
                        // 舊版瀏覽器可能不支援直接設置files屬性
                        console.warn('[修復腳本] DataTransfer API 不支援，嘗試直接設置檔案');
                        originalInput.files = e.target.files;
                    }
                    
                    // 手動觸發 change 事件
                    const event = new Event('change', { bubbles: true });
                    originalInput.dispatchEvent(event);
                    console.log('[修復腳本] 成功設置檔案並觸發變更事件');
                } catch (err) {
                    console.error('[修復腳本] 設置檔案失敗:', err);
                    showMessage(getTranslation('file_selection_error') || '選擇檔案後發生錯誤，請重試', 'error');
                }
            } else {
                console.error('[修復腳本] 找不到原始檔案輸入元素');
            }
        } else {
            console.log('[修復腳本] 未選擇檔案或選擇被取消');
        }
          // 清理臨時輸入框
        document.body.removeChild(tempInput);
    };
}

// 顯示訊息函數
function showMessage(message, type = 'success') {
    // 嘗試使用現有的成功/錯誤消息框
    if (type === 'success') {
        const successDiv = document.getElementById('success-message');
        if (successDiv) {
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
            return;
        }
    } else {
        const errorDiv = document.getElementById('error-message');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
            return;
        }
    }
    
    // 如果找不到現有的消息框，創建一個新的浮動消息
    const messageDiv = document.createElement('div');
    messageDiv.style.position = 'fixed';
    messageDiv.style.bottom = '20px';
    messageDiv.style.right = '20px';
    messageDiv.style.padding = '15px';
    messageDiv.style.borderRadius = '5px';
    messageDiv.style.zIndex = 9999;
    messageDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    messageDiv.textContent = message;
    
    if (type === 'success') {
        messageDiv.style.backgroundColor = '#4CAF50';
        messageDiv.style.color = 'white';
    } else {
        messageDiv.style.backgroundColor = '#f44336';
        messageDiv.style.color = 'white';
    }
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.opacity = '0';
        messageDiv.style.transition = 'opacity 0.5s';
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 500);
    }, 4000);
}

// 格式化檔案大小函數
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}
