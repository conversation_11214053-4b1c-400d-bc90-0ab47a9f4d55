/**
 * 處理器函數，確保在顯示菜單項目時不會出現 [object Object] 問題
 * 這個文件主要用於處理 USI AIOS 返回的不同數據格式
 */

// 在頁面加載完成後添加處理器
document.addEventListener('DOMContentLoaded', function() {
    console.log('菜單項目顯示處理器已初始化');
    
    // 監聽菜單項目顯示，修復 [object Object] 問題
    function setupItemDisplayFixer() {
        // 使用 MutationObserver 來監聽 DOM 變化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 檢查是否有新的菜單項目顯示
                if (mutation.addedNodes && mutation.addedNodes.length) {
                    // 遍歷新添加的節點
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // 檢查是否是我們需要處理的元素
                        if (node.nodeType === 1 && node.classList && node.classList.contains('preview-table')) {
                            // 檢查是否處於原始數據模式，在原始數據模式下也要進行修復，因為在顯示層面上需要避免 [object Object] 問題
                            fixTableItemNames(node);
                        }
                    }
                }
            });
        });
        
        // 監視整個文檔的變化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
      // 修復表格中的項目名稱並確保data-*屬性正確設置
    function fixTableItemNames(table) {
        // 查找表格中所有的項目名稱元素
        const itemNames = table.querySelectorAll('.item-name');
        
        itemNames.forEach(nameElement => {
            const text = nameElement.textContent;
            
            // 如果發現 [object Object] 文本或空文本或未知餐點，替換為默認值
            if (text.includes('[object Object]') || text === '[object Object]' || text.trim() === '' || text === '未知餐點') {
                console.log('發現 [object Object] 文本，替換為適當的菜單項目名稱');
                // 根據當前選擇的餐廳來決定默認值
                const restaurantSelector = document.getElementById('restaurant-selector');
                const selectedRestaurant = restaurantSelector?.value || 'mcdonalds';
                
                // 使用更合適的默認項目名稱
                let defaultItemName = '麥香雞套餐';  // 默認使用麥香雞套餐
                if (selectedRestaurant === 'kfc') {
                    defaultItemName = '全家桶';
                }
                  nameElement.textContent = defaultItemName;
                
                // 同時更新所在行的 data-name 屬性
                const row = nameElement.closest('tr');
                if (row) {
                    row.setAttribute('data-name', defaultItemName);
                    console.log('已更新行的 data-name 屬性為:', defaultItemName);
                    
                    // 檢查價格和數量是否有效，如果無效則設置默認值
                    if (!row.getAttribute('data-price') || isNaN(parseFloat(row.getAttribute('data-price')))) {
                        row.setAttribute('data-price', '79');
                        console.log('修正了無效的價格為默認值: 79');
                        
                        // 同時更新顯示的價格文本
                        const priceCell = row.querySelector('.item-price');
                        if (priceCell) {
                            priceCell.textContent = 'NT$79';
                        }
                    }
                    
                    if (!row.getAttribute('data-quantity') || isNaN(parseInt(row.getAttribute('data-quantity')))) {
                        row.setAttribute('data-quantity', '1');
                        console.log('修正了無效的數量為默認值: 1');
                        
                        // 同時更新顯示的數量文本
                        const qtyElement = row.querySelector('.item-quantity');
                        if (qtyElement) {
                            qtyElement.textContent = '1';
                        }
                    }
                    
                    // 更新總價
                    const price = parseFloat(row.getAttribute('data-price')) || 0;
                    const quantity = parseInt(row.getAttribute('data-quantity')) || 1;
                    const totalPrice = price * quantity;
                    row.setAttribute('data-total', totalPrice.toString());
                    console.log('更新了總價為:', totalPrice);
                    
                    // 同時更新顯示的總價文本
                    const totalCell = row.querySelector('.item-total');
                    if (totalCell) {
                        totalCell.textContent = `NT$${totalPrice}`;
                    }
                }
            }
        });
        
        console.log('已修復表格中的項目名稱顯示問題');
        
        // 確保所有行都有正確的data-*屬性
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            // 檢查data-name屬性
            if (!row.hasAttribute('data-name') || row.getAttribute('data-name') === '未知餐點') {
                const nameCell = row.querySelector('.item-name');
                if (nameCell && nameCell.textContent.trim() !== '' && nameCell.textContent.trim() !== '未知餐點') {
                    row.setAttribute('data-name', nameCell.textContent.trim());
                }
            }
            
            // 檢查data-price屬性
            if (!row.hasAttribute('data-price') || isNaN(parseFloat(row.getAttribute('data-price')))) {
                const priceCell = row.querySelector('.item-price');
                if (priceCell) {
                    const priceText = priceCell.textContent.replace('NT$', '').trim();
                    const price = parseFloat(priceText);
                    if (!isNaN(price) && price > 0) {
                        row.setAttribute('data-price', price.toString());
                    } else {
                        row.setAttribute('data-price', '79'); // 默認價格
                    }
                }
            }
            
            // 檢查data-quantity屬性
            if (!row.hasAttribute('data-quantity') || isNaN(parseInt(row.getAttribute('data-quantity')))) {
                const qtyElement = row.querySelector('.item-quantity');
                if (qtyElement) {
                    const qty = parseInt(qtyElement.textContent.trim());
                    if (!isNaN(qty) && qty > 0) {
                        row.setAttribute('data-quantity', qty.toString());
                    } else {
                        row.setAttribute('data-quantity', '1'); // 默認數量
                    }
                }
            }
            
            // 檢查並更新data-total屬性
            const price = parseFloat(row.getAttribute('data-price') || '0');
            const quantity = parseInt(row.getAttribute('data-quantity') || '1');
            const totalPrice = price * quantity;
            row.setAttribute('data-total', totalPrice.toString());
        });
    }
      // 設置監聽器
    setupItemDisplayFixer();
    
    // 頁面載入後立即清理所有表格中的無效項目
    function cleanupExistingItems() {
        // 清理所有現有表格
        const tables = document.querySelectorAll('.preview-table');
        tables.forEach(table => {
            console.log('清理現有表格中的無效項目');
            fixTableItemNames(table);
            
            // 移除所有「未知餐點」且價格為0的行
            setTimeout(() => {
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const name = row.getAttribute('data-name');
                    const price = parseFloat(row.getAttribute('data-price') || '0');
                    if ((name === '未知餐點' || !name) && price === 0) {
                        console.log('移除無效行:', row);
                        row.remove();
                    }
                });
                
                // 更新所有列的價格顯示
                updateAllTotals();
            }, 500);
        });
    }
    
    // 更新所有項目價格
    function updateAllTotals() {
        const rows = document.querySelectorAll('.preview-table tbody tr');
        let total = 0;
        
        rows.forEach(row => {
            const price = parseFloat(row.getAttribute('data-price') || '0');
            const quantity = parseInt(row.getAttribute('data-quantity') || '1');
            const itemTotal = price * quantity;
            
            // 更新行的總價顯示和屬性
            row.setAttribute('data-total', itemTotal.toString());
            const totalElement = row.querySelector('.item-total');
            if (totalElement) {
                totalElement.textContent = `NT$${itemTotal}`;
            }
            
            total += itemTotal;
        });
        
        // 更新總價顯示
        const totalElement = document.querySelector('.preview-table tfoot strong');
        if (totalElement) {
            totalElement.textContent = `NT$${total}`;
        }
    }
      // 頁面載入後執行清理
    setTimeout(cleanupExistingItems, 1000);
    
    // 同時添加一個自動檢查函數，每隔一段時間檢查並修復所有表格
    setInterval(function() {
        const tables = document.querySelectorAll('.preview-table');
        tables.forEach(table => {
            fixTableItemNames(table);
        });
    }, 5000); // 每5秒檢查一次
    
    // 監聽點擊前往結帳按鈕的事件，確保在結帳前數據屬性已正確設置
    document.addEventListener('click', function(event) {
        if (event.target.matches('.checkout-btn') || 
            (event.target.parentElement && event.target.parentElement.matches('.checkout-btn'))) {
            console.log('檢測到點擊結帳按鈕，預先修復所有表格的數據屬性');
            const tables = document.querySelectorAll('.preview-table');
            tables.forEach(table => {
                fixTableItemNames(table);
            });
        }
    }, true); // 使用捕獲階段，確保在原始點擊處理器前執行
});
