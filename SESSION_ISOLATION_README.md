# 會話隔離機制實施文檔

## 概述

本文檔說明了自然語言點餐系統中實施的多用戶會話隔離機制，解決了多用戶同時使用時 APPprompt 混亂的問題。

## 問題背景

### 原始問題
- 多用戶同時使用系統時，Gemini AI 只會使用最新的 APPprompt 作為 system prompt
- 用戶A上傳麥當勞日文菜單，用戶B上傳KFC中文菜單時，兩者會互相干擾
- 導致用戶找不到正確的菜單項目，服務混亂

### 解決目標
- 實現完全的用戶會話隔離
- 支援無限多用戶同時使用
- 適合 Docker 部署環境
- 保持向後兼容性

## 解決方案架構

### 核心設計理念
採用 **純前端會話管理 + API 即時傳遞** 的方案：

1. **前端會話管理**：每個用戶瀏覽器維護獨立的會話狀態
2. **即時 APPprompt 傳遞**：API 調用時直接傳遞 APPprompt 內容
3. **無狀態後端**：後端不保存會話狀態，適合 Docker 部署

### 技術實施

#### 1. 會話管理器 (SessionManager)
**文件位置**: `public/js/session-manager.js`

**主要功能**:
- 生成唯一會話ID
- 管理 APPprompt、菜單數據、語言設置
- 使用 sessionStorage 進行本地存儲
- 提供統一的 API 接口

**核心方法**:
```javascript
class SessionManager {
  constructor()                              // 初始化會話
  getOrCreateSessionId()                     // 獲取或創建會話ID
  setAppPrompt(appPrompt)                    // 設置 APPprompt
  getAppPrompt()                             // 獲取 APPprompt
  setMenuData(menuData)                      // 設置菜單數據
  getMenuData()                              // 獲取菜單數據
  processOrder(customerInput)                // 處理點餐請求
  generateAppPrompt(type, content, language) // 生成 APPprompt
  clearSession()                             // 清除會話數據
  getSessionStatus()                         // 獲取會話狀態
}
```

#### 2. 後端 API 修改

**修改的文件**:
- `src/services/GeminiService.ts`
- `src/services/PromptEngine.ts`
- `src/routes/prompt.ts`

**主要變更**:
- 所有相關方法添加 `sessionId` 參數
- API 接受前端傳遞的 `appPrompt` 參數
- 日誌輸出包含會話ID前綴，便於追蹤

**API 變更示例**:
```typescript
// 點餐 API
POST /api/prompt/process-order
{
  "customerRequest": "我要一個大麥克套餐",
  "language": "zh-TW",
  "sessionId": "session_1234567890_abc123",
  "appPrompt": "{...完整的APPprompt內容...}"
}

// APPprompt 生成 API
POST /api/prompt/generate
{
  "type": "natural",
  "content": "...",
  "language": "zh-TW",
  "sessionId": "session_1234567890_abc123"
}
```

#### 3. 前端整合

**修改的文件**:
- `public/index.html` - 添加會話狀態顯示
- `public/js/editor.js` - 整合會話管理器

**主要變更**:
- 菜單上傳成功後保存到會話管理器
- APPprompt 生成後保存到會話管理器
- 點餐請求使用會話管理器處理
- 添加會話狀態實時顯示

#### 4. 會話狀態顯示

**功能**:
- 顯示當前會話ID
- 實時顯示菜單載入狀態
- 實時顯示 APPprompt 生成狀態
- 顯示當前語言設置
- 提供清除會話功能

## 使用流程

### 用戶A的操作流程
1. 打開系統，自動生成會話ID: `session_A_1234567890_abc123`
2. 上傳麥當勞日文菜單 → 保存到會話A
3. 生成 APPprompt → 保存到會話A
4. 進行日文點餐 → 使用會話A的 APPprompt

### 用戶B的操作流程（同時進行）
1. 打開系統，自動生成會話ID: `session_B_1234567890_def456`
2. 上傳KFC中文菜單 → 保存到會話B
3. 生成 APPprompt → 保存到會話B
4. 進行中文點餐 → 使用會話B的 APPprompt

### 隔離效果
- 用戶A和用戶B的操作完全獨立
- 各自使用正確的菜單和 APPprompt
- 不會互相干擾

## Docker 部署優勢

### 無狀態設計
- 後端不保存會話狀態
- 容器重啟不影響用戶體驗
- 支援水平擴展

### 多實例支援
- 可以部署多個容器實例
- 負載均衡器可以隨意分配請求
- 每個請求都包含完整的上下文信息

## 測試驗證

### 測試頁面
**文件位置**: `public/session-test.html`

**測試功能**:
- 模擬多個用戶同時使用
- 驗證會話隔離效果
- 批量測試功能
- 並發點餐測試

### 測試場景
1. **基本隔離測試**
   - 用戶A: 麥當勞日文菜單
   - 用戶B: KFC中文菜單
   - 用戶C: 星巴克英文菜單

2. **並發測試**
   - 同時處理多個點餐請求
   - 驗證每個請求使用正確的 APPprompt

3. **會話清除測試**
   - 測試會話清除功能
   - 驗證數據完全清除

## 性能考量

### 前端存儲
- 使用 sessionStorage，瀏覽器關閉後自動清除
- APPprompt 大小通常在 10-50KB，對性能影響微小
- 菜單數據大小通常在 5-20KB

### 網路傳輸
- 每次點餐請求需要傳遞完整 APPprompt
- 增加的網路負載約 10-50KB
- 對於現代網路環境影響很小

### 後端處理
- 無需維護會話狀態，減少內存使用
- 每個請求都是獨立的，便於擴展
- 日誌包含會話ID，便於問題追蹤

## 安全考量

### 數據隔離
- 每個會話的數據完全隔離
- 無法訪問其他用戶的會話數據
- sessionStorage 僅限當前瀏覽器標籤頁

### 會話ID 安全
- 使用時間戳 + 隨機字符串生成
- 足夠的隨機性防止猜測
- 不包含敏感信息

## 監控和調試

### 日誌格式
```
[Session:session_1234567890_abc123] === 開始處理自然語言點餐 ===
[Session:session_1234567890_abc123] 顧客輸入: 我要一個大麥克套餐
[Session:session_1234567890_abc123] APPprompt 可用: true
```

### 會話狀態追蹤
- 前端會話狀態實時顯示
- 後端日誌包含會話ID
- 便於問題定位和調試

## 未來擴展

### 可能的改進
1. **會話持久化**：可選的會話數據持久化到後端
2. **用戶認證**：整合用戶登入系統
3. **會話共享**：支援多設備會話同步
4. **會話分析**：會話使用情況統計

### 兼容性
- 當前實施保持向後兼容
- 可以逐步添加新功能
- 不影響現有用戶體驗

## 總結

會話隔離機制成功解決了多用戶同時使用的問題：

✅ **完全隔離**：每個用戶會話都有獨立的 APPprompt  
✅ **即時生效**：不需要等待文件寫入/讀取  
✅ **擴展性好**：支援無限多用戶同時使用  
✅ **Docker 友好**：適合容器化部署  
✅ **實施簡單**：主要是 API 參數調整  
✅ **向後兼容**：保留現有功能  

這個解決方案為系統提供了穩定、可擴展的多用戶支援能力。
