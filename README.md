# 自然語言點餐系統 (Natural Order)

一個基於自然語言處理的智慧點餐系統，支援 BDD/AAprompt 輸入和菜單管理，讓用戶可以使用自然語言（文字或語音）進行餐點訂購。整合了 Google Gemini AI，提供更智能的點餐體驗。

## 功能特點

- **自然語言處理**：
  - 使用先進的 NLP 技術分析用戶輸入
  - 支援模糊匹配和嚴格匹配模式
  - 智能識別餐點數量和修飾詞
  - 多語言支援（中文/英文）

- **語音識別與合成**：
  - 支援中文語音輸入（使用 Web Speech API）
  - 即時語音識別反饋
  - 訂單確認語音播報
  - 降噪處理提高識別準確率

- **BDD/AAprompt支援**：
  - 支援行為驅動開發 (BDD) 格式進行功能描述
  - 支援 Actor-Action (AAprompt) 格式設計提示詞
  - 自動轉換為 APPprompt 格式用於大語言模型
  - 整合 Google Gemini AI 提升生成質量
  - 動態 prompt 生成與優化

- **菜單管理**：
  - 支援多種格式菜單上傳（CSV/Excel/JSON）
  - 自動菜單分類和驗證
  - 模糊匹配算法提高餐點識別準確率
  - 支援多語言餐點名稱
  - 菜單版本控制

- **訂單系統**：
  - 實時訂單確認與訂單摘要顯示
  - Gemini AI 智能訂單分析與確認
  - 多步驟訂單流程與狀態追蹤
  - 訂單修改功能（新增/刪除/修改餐點）
  - 智能價格計算與總金額顯示
  - 訂單確認對話框與語音提示
  - 完整的錯誤處理與用戶反饋

- **Firebase整合**：
  - 即時數據同步
  - 菜單數據與訂單資料儲存與檢索
  - 用戶訂單歷史追蹤
  - 安全的數據存取控制

## 技術棧

- **前端**：
  - HTML5, CSS3, JavaScript (ES6+)
  - CodeMirror（用於 BDD/AAprompt 編輯）
  - Web Speech API
  - 響應式設計

- **後端**：
  - Node.js
  - Express.js
  - TypeScript
  - Google Generative AI (Gemini)

- **資料庫**：
  - Firebase/Firestore
  - 即時數據同步

- **自然語言處理**：
  - 自訂的 NLP 服務
  - 模糊匹配算法
  - Google Gemini AI 整合

- **開發工具**：
  - TypeScript
  - ESLint
  - Jest（測試框架）
  - Git 版本控制

## 快速開始

### 方式一：Docker 部署（推薦）

#### 環境需求
- Docker Engine 20.10+
- Docker Compose 2.0+
- Google Gemini AI API Key ([獲取方式](https://makersuite.google.com/app/apikey))

#### 快速部署

1. **克隆專案**：
   ```bash
   git clone <repository-url>
   cd natural-order
   ```

2. **配置環境變數**：
   ```bash
   cp .env.example .env
   # 編輯 .env 文件，填入您的 Firebase 配置和 Gemini API Key
   # GEMINI_API_KEY=your-real-api-key-here
   # USE_GEMINI_AI=true
   ```

3. **使用部署腳本**：
   
   **Windows (PowerShell)**：
   ```powershell
   .\docker-build.ps1
   ```
   
   **Linux/macOS**：
   ```bash
   chmod +x docker-build.sh
   ./docker-build.sh
   ```

4. **手動部署**：
   ```bash
   # 構建並啟動服務
   docker-compose up -d --build
   
   # 查看服務狀態
   docker-compose ps
   ```

5. **訪問應用**：
   打開瀏覽器訪問 http://localhost:3005

⚠️ **重要提醒：** 系統需要有效的 Gemini AI API Key 才能正常運行 APPprompt 生成功能。

📖 **詳細的 Docker 部署指南請參考 [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)**

### 方式二：傳統部署

#### 環境需求
- Node.js 18+
- npm 或 yarn
- Firebase 帳戶
- Google Gemini API 金鑰

#### 安裝與運行

1. **克隆專案**：
   ```bash
   git clone <repository-url>
   cd natural-order
   ```

2. **安裝依賴**：
   ```bash
   npm install
   ```

3. **環境配置**：
   複製 `.env.example` 為 `.env` 並填入配置：
   ```bash
   cp .env.example .env
   ```

4. 編譯 TypeScript：
   ```bash
   npm run build
   ```

5. 啟動服務器：
   ```bash
   npm start
   ```
   
   或者直接開發模式運行：
   ```bash
   npm run dev
   ```

6. 訪問系統：
   - 開啟瀏覽器，訪問 `http://localhost:3005`

## 使用指南

### 菜單管理

1. 點擊「菜單管理」標籤
2. 上傳菜單文件（支援 CSV/Excel/JSON）
3. 系統會自動處理與顯示餐點信息
4. 可以預覽和編輯菜單內容
5. 支援菜單版本管理

### 使用 BDD 編輯器

1. 點擊「BDD 編輯器」標籤
2. 使用標準 BDD 語法編寫測試用例：
   ```gherkin
   Feature: 功能描述
   
   Scenario: 場景描述
     Given 前提條件
     When 執行動作
     Then 期望結果
   ```
3. 使用即時預覽功能查看結果
4. 可以保存和載入模板

### 使用 AAprompt 編輯器

1. 點擊「AAprompt 編輯器」標籤
2. 使用結構化表單或直接編輯模式：
   ```
   作為[角色]，[動作]，在[情境]的情況下，[限制條件]
   ```
3. 支援模板庫和歷史記錄
4. 提供即時驗證和提示

### 自然語言點餐

1. 點擊「自然語言點餐」標籤
2. 選擇輸入方式：
   - 文字輸入：直接輸入如「我要一個大麥克和一杯可樂」
   - 語音輸入：點擊麥克風按鈕進行語音點餐
3. 等待 Gemini AI 分析並回應：
   - AI 會自動分析您的點餐需求
   - 提供智能的餐點建議和價格信息
   - 生成自然語言形式的訂單確認
4. 確認訂單：
   - 點擊「確認訂單」按鈕
   - 查看詳細的訂單摘要和總金額
   - 確認或修改訂單內容
5. 完成訂購流程

### Gemini AI 智能點餐

系統整合了 Google Gemini AI，提供以下智能功能：
- 自然語言理解：準確理解複雜的點餐需求
- 智能推薦：根據菜單內容提供個性化建議
- 價格計算：自動計算餐點價格和總金額
- 對話式交互：提供自然的對話體驗
- 錯誤處理：智能處理不明確或錯誤的輸入

## 開發指南

### 目錄結構

```
project/
├── public/          # 前端靜態文件
│   ├── css/         # 樣式文件
│   ├── js/          # 前端腳本
│   └── index.html   # 主頁面
├── src/             # 後端源代碼
│   ├── config/      # 配置文件
│   ├── routes/      # API路由
│   ├── services/    # 服務層
│   └── types/       # TypeScript類型
├── test/            # 測試文件
├── document/        # 文檔和示例
└── uploads/         # 上傳文件暫存
```

### API 參考

系統提供 RESTful API：

#### 自然語言處理
- `POST /api/nlp/process` - 處理自然語言輸入
- `GET /api/nlp/menu/:restaurantId` - 獲取菜單

#### 菜單管理
- `POST /api/menu/upload` - 上傳菜單文件
- `GET /api/menu/:restaurantId` - 獲取菜單
- `PUT /api/menu/:restaurantId` - 更新菜單

#### Gemini AI 處理
- `POST /api/prompt/process-order` - Gemini AI 自然語言點餐處理
- `POST /api/prompt/validate-bdd` - 驗證 BDD 語法
- `POST /api/prompt/validate-aaprompt` - 驗證 AAprompt 語法
- `POST /api/prompt/generate-from-bdd` - 從 BDD 生成 APPprompt
- `POST /api/prompt/generate-from-aaprompt` - 從 AAprompt 生成 APPprompt
- `POST /api/prompt/generate-from-natural` - 從自然語言生成 APPprompt

#### 訂單管理
- `POST /api/order` - 創建訂單
- `GET /api/order/:id` - 獲取訂單
- `PUT /api/order/:id` - 更新訂單
- `PUT /api/order/:id/status` - 更新訂單狀態

## 最新功能亮點

### 🤖 Gemini AI 智能點餐助手
- **自然對話**：支援完全自然的中文對話式點餐
- **智能理解**：準確理解複雜的點餐需求和修飾詞
- **即時回應**：提供即時的 AI 回應和建議
- **價格透明**：自動計算並顯示餐點價格和總金額

### ✅ 完善的訂單確認流程
- **智能解析**：自動從 AI 回應中提取訂單信息
- **確認對話框**：美觀的訂單確認界面
- **語音提示**：語音播報訂單確認信息
- **錯誤處理**：完善的錯誤處理和用戶反饋

### 🎯 增強的用戶體驗
- **響應式設計**：支援各種設備和螢幕尺寸
- **即時反饋**：所有操作都有即時的視覺和聲音反饋
- **無縫整合**：前後端緊密整合，提供流暢體驗

## 測試

### 單元測試
```bash
npm test
```

### 功能測試
```bash
# 測試 Gemini AI 點餐功能
node test_gemini_order.js

# 測試完整工作流程
node test_complete_workflow.js

# 測試訂單確認修復
node test_confirm_order_fix.js
```

### 手動測試
```bash
# 啟動服務器進行手動測試
npm start
# 然後訪問 http://localhost:3005
```

## 故障排除

### 常見問題

**Q: 點擊「確認訂單」按鈕沒有反應**
A: 這個問題已在 v1.2.0 中修復。請確保使用最新版本並重新載入頁面。

**Q: Gemini AI 無法回應**
A: 請檢查：
- Google Gemini API 金鑰是否正確配置
- 網路連線是否正常
- 環境變數 `USE_GEMINI_AI=true` 是否設置

**Q: 語音識別無法使用**
A: 請確保：
- 使用支援 Web Speech API 的瀏覽器（Chrome 推薦）
- 授予麥克風權限
- 檢查麥克風是否正常工作

**Q: 菜單上傳失敗**
A: 請確保：
- 檔案格式為 CSV、Excel 或 JSON
- 檔案結構符合系統要求
- 檔案大小在限制範圍內

### 開發者除錯

```bash
# 查看詳細日誌
npm run dev

# 檢查 API 狀態
curl http://localhost:3005/api/health

# 測試特定功能
node test_specific_function.js
```

## 貢獻指南

1. Fork 專案
2. 創建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

### 開發規範

- 遵循 TypeScript 編碼規範
- 所有新功能都需要包含測試
- 保持代碼覆蓋率在 80% 以上
- 使用有意義的 commit 訊息

## 版本歷史

- **1.0.0**
  - 初始版本發布
  - 基本的自然語言點餐功能
  - BDD/AAprompt 支援
  - 菜單管理系統

- **1.1.0**
  - 添加語音識別功能
  - 改進菜單管理系統
  - 整合 Google Gemini AI
  - 基礎的訂單處理功能

- **1.2.0** (最新版本)
  - 🚀 完整整合 Gemini AI 智能點餐
  - ✅ 修復訂單確認對話框問題
  - 🎯 智能訂單解析與價格計算
  - 🔊 增強語音提示功能
  - 🛠️ 改進錯誤處理機制
  - 📱 優化用戶界面體驗
  - 🔧 完善的測試覆蓋

## 許可證

本專案使用 ISC 許可證 - 詳見 [LICENSE](LICENSE) 文件

## 致謝

- **Google Generative AI 團隊** - 提供強大的 Gemini AI 服務
- **Firebase 團隊** - 提供可靠的雲端數據庫解決方案
- **Mozilla 開發者網路 (MDN)** - Web Speech API 文檔和支援
- **TypeScript 團隊** - 提供優秀的類型安全開發體驗
- **Express.js 社群** - 提供靈活的 Node.js 框架
- **所有貢獻者和測試用戶** - 協助改進系統功能和用戶體驗

### 技術支援
- [Google Gemini AI 官方文檔](https://ai.google.dev/)
- [Firebase 官方文檔](https://firebase.google.com/docs)
- [Web Speech API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API)
- [TypeScript 官方文檔](https://www.typescriptlang.org/docs/)
