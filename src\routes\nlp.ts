// 這是一個修復路由問題的示例解決方案

// 1. 使用 npm 更新 Express 和 TypeScript 類型定義
// npm install --save express@latest @types/express@latest

// 2. 修改 nlp.ts 文件
import { Router, Request, Response } from 'express';
import { MenuProcessor } from '../services/MenuProcessor.js';
import { NLPService } from '../services/NLPService.js';
import { MenuItem } from '../types/menu.js';
import path from 'node:path';
import fs from 'node:fs';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = Router();
const nlpService = new NLPService();
const menuProcessor = new MenuProcessor();

// 臨時存儲加載的菜單數據
export const loadedMenus: { [key: string]: MenuItem[] } = {};

// 不再自動載入默認菜單，改為由用戶上傳菜單時動態載入
console.log("系統啟動，不預載菜單，等待用戶上傳");

// 添加菜單項目到 loadedMenus
export function updateLoadedMenu(restaurantId: string, menuData: any) {
  const items: MenuItem[] = [];
  if (menuData && menuData.categories) {
    menuData.categories.forEach((category: any) => {
      if (category && Array.isArray(category.items)) {
        items.push(...category.items);
      }
    });
  }
  
  if (items.length > 0) {
    loadedMenus[restaurantId] = items;
    console.log(`已更新 ${restaurantId} 的菜單，共 ${items.length} 個項目`);
    console.log('當前可用餐廳菜單:', Object.keys(loadedMenus));
    return true;
  }
  
  return false;
}

// 定義處理器函數
// 自然語言處理 API
const processHandler = async (req: Request, res: Response) => {
  try {
    const { text, input, context, strictMode } = req.body;
    
    // 支援兩種輸入格式：text (直接文本) 和 input (舊版相容)
    const userInput = text || input;
    
    if (!userInput) {
      return res.status(400).json({
        success: false,
        error: '請提供輸入文本'
      });
    }

    console.log('[NLP Route] 開始處理自然語言訂單:', userInput);    // 使用簡化的 NLPService，完全依賴 Gemini AI 處理
    try {
      const result = await nlpService.processOrderWithAppPromptFirst(userInput);

      return res.json({
        success: result.success,
        data: result.data,
        processed: {
          entities: result.analysis?.entities || []
        },
        analysis: result.analysis,
        error: result.error
      });

    } catch (error: any) {
      console.error('[NLP Route] NLP 服務處理失敗:', error.message);
      return res.status(500).json({
        success: false,
        error: `處理文本時發生錯誤: ${error.message}`,
        data: { matches: [], unidentified: [userInput] },
        analysis: {
          aiProcessed: false,
          error: error.message
        }
      });
    }
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `處理文本時發生錯誤: ${error.message}`
    });
  }
};

// 處理訂單修改 - 已簡化，不再支援複雜的修改操作
const modifyHandler = async (req: Request, res: Response) => {
  try {
    const { input, currentItems, context, strictMode } = req.body;
    
    if (!input || !currentItems || !Array.isArray(currentItems)) {
      return res.status(400).json({
        success: false,
        error: '請提供輸入文本和當前餐點列表'
      });
    }

    // 簡化的修改處理：將修改請求當作新訂單處理
    console.log('[NLP Route] 處理訂單修改請求:', input);
    
    const result = await nlpService.processOrderWithAppPromptFirst(input);
    
    res.json({
      success: true,
      result: {
        action: "new_order",
        message: "修改請求已作為新訂單處理",
        aiResponse: result.data?.aiResponse,
        originalItems: currentItems,
        modificationInput: input
      }
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `處理修改指令時發生錯誤: ${error.message}`
    });
  }
};

// 獲取指定餐廳的菜單
const getMenuHandler = (req: Request, res: Response) => {
  const { restaurantId } = req.params;
  
  if (!loadedMenus[restaurantId]) {
    return res.json({
      success: true,
      menu: [],
      message: "尚未上傳此餐廳的菜單，請先在菜單管理頁面上傳菜單。"
    });
  }
  
  res.json({
    success: true,
    menu: loadedMenus[restaurantId]
  });
};

// 註冊路由
router.post('/process', function(req, res) {
  processHandler(req, res);
});
router.post('/modify', function(req, res) {
  modifyHandler(req, res);
});
router.get('/menu/:restaurantId', function(req, res) {
  getMenuHandler(req, res);
});

// 添加手動觸發載入默認菜單的路由（可選功能）
router.post('/load-default-menus', async function(req, res) {
  try {
    const result = await loadDefaultMenus();
    res.json({
      success: true,
      message: '默認菜單載入操作完成',
      availableMenus: Object.keys(loadedMenus)
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: `載入默認菜單失敗: ${error.message}`
    });
  }
});

// 載入默認菜單 - 此功能現已變為可選，不會自動調用
// 可以通過特定API端點觸發，如果將來需要的話
async function loadDefaultMenus() {
  try {
    console.log('開始載入默認菜單...');
    // 載入麥當勞菜單
    const mcdonaldsMenu: MenuItem[] = [];
    
    // 檢查 document/Mcdonalds_menu 目錄是否存在
    const menuDirPath = path.join(__dirname, '../../document/Mcdonalds_menu');
    if (!fs.existsSync(menuDirPath)) {
      console.log('找不到默認菜單目錄:', menuDirPath);
      return;
    }
    
    // 讀取漢堡菜單
    const burgersPath = path.join(menuDirPath, 'burgers.csv');
    if (fs.existsSync(burgersPath)) {
      const result = await menuProcessor.processMenuFile(
        burgersPath,
        'mcdonalds',
        { autoDetectCategory: true }
      );
      
      if (result.success && result.data) {
        result.data.categories.forEach(category => {
          mcdonaldsMenu.push(...category.items);
        });
      }
    } else {
      console.log('找不到漢堡菜單文件:', burgersPath);
    }
      
    // 其餘的菜單文件處理代碼保留但不主動執行
    
    // 如果有加載到菜單項目，則添加到已載入菜單中
    if (mcdonaldsMenu.length > 0) {
      loadedMenus['mcdonalds'] = mcdonaldsMenu;
      console.log('麥當勞菜單載入完成，共', mcdonaldsMenu.length, '個項目');
    }
    
    console.log('默認菜單載入完成');
    return true;
  } catch (error: any) {
    console.error('載入默認菜單時發生錯誤:', error.message);
    return false;
  }
}

export default router;
