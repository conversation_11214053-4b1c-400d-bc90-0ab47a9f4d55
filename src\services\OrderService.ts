import { db, collection, doc, getDoc, updateDoc, addDoc, Timestamp } from '../config/firebase.js';

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
}

interface Order {
  id?: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'delivered';
  timestamp: Timestamp;
  userId?: string;
}

export class OrderService {  // 創建新訂單
  async createOrder(items: OrderItem[], userId: string | null = null): Promise<Order> {
    try {
      // 記錄收到的原始訂單項目
      console.log('收到訂單項目:', JSON.stringify(items, null, 2));
      
      // 合併相同商品的訂單項目
      const mergedItems = this.mergeOrderItems(items);
      console.log('合併後的訂單項目:', JSON.stringify(mergedItems, null, 2));
      
      // 計算總金額
      const totalAmount = mergedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        // 創建訂單數據基本結構，避免包含 undefined 值
      const orderData: Order = {
        items: mergedItems,
        totalAmount,
        status: 'pending',
        timestamp: Timestamp.now()
      };
        // 嚴格檢查 userId，確保只有有效的字串才會添加到訂單數據中
      if (userId !== null && userId !== undefined && typeof userId === 'string' && userId.trim() !== '') {
        orderData.userId = userId;
        console.log('添加有效的 userId 到訂單:', userId);
      } else {
        console.log('未添加 userId 到訂單，值無效或為空:', userId);
        // 明確刪除 userId 字段，確保不會有 undefined 值傳入 Firebase
        if ('userId' in orderData) {
          delete orderData.userId;
        }
      }
      
      try {
        // 記錄要保存的訂單數據
        console.log('準備創建訂單，總計項目數:', mergedItems.length);
        console.log('訂單總金額:', totalAmount);
        
        const ordersCollection = collection(db, 'orders');
        const docRef = await addDoc(ordersCollection, orderData);
        console.log('訂單已成功創建，ID:', docRef.id);
        
        return { ...orderData, id: docRef.id };
      } catch (error) {
        console.error('創建訂單失敗:', error);
        throw error;
      }
    } catch (error) {
      console.error('處理訂單項目時出錯:', error);
      throw error;
    }
  }
    // 合併相同商品的訂單項目並過濾測試數據
  private mergeOrderItems(items: OrderItem[]): OrderItem[] {
    // 先過濾無效和測試數據
    const validItems = items.filter(item => 
      item && 
      item.name && 
      typeof item.name === 'string' &&
      // 排除純數字名稱（可能是測試數據）
      !/^\d+$/.test(item.name.trim()) &&
      typeof item.quantity === 'number' && item.quantity > 0 &&
      typeof item.price === 'number' && item.price >= 0
    );
    
    console.log('過濾後的有效項目數:', validItems.length, '原始項目數:', items.length);
    
    // 使用 Map 根據商品名稱合併相同的商品
    const itemMap = new Map<string, OrderItem>();
      for (const item of validItems) {
      // 清理項目名稱，移除多餘的換行、空格，以及"精確匹配"等標記
      const cleanName = item.name
        .replace(/\n\s+/g, ' ')          // 將換行和多個空格替換為單一空格
        .replace(/精確匹配.*$/, '')       // 移除"精確匹配"及之後的內容
        .trim();                         // 移除前後空格
      
      if (itemMap.has(cleanName)) {
        // 如果已存在相同名稱的商品，增加數量
        const existingItem = itemMap.get(cleanName)!;
        existingItem.quantity += item.quantity;
      } else {
        // 否則添加新項目，使用清理後的名稱
        itemMap.set(cleanName, { ...item, name: cleanName });
      }
    }
    
    // 將 Map 轉換為數組並返回
    return Array.from(itemMap.values());
  }
    // 確認訂單
  async confirmOrder(orderId: string): Promise<Order> {
    const orderRef = doc(db, 'orders', orderId);
    
    await updateDoc(orderRef, {
      status: 'confirmed'
    });
    
    // 在實際場景中，這裡可能還會觸發其他系統，如廚房顯示系統等
    setTimeout(async () => {
      await updateDoc(orderRef, { status: 'preparing' });
    }, 60000); // 模擬1分鐘後開始準備訂單
    
    const orderSnapshot = await getDoc(orderRef);
    return { id: orderId, ...orderSnapshot.data() as Omit<Order, 'id'> };
  }
    // 獲取訂單資訊
  async getOrder(orderId: string): Promise<Order | null> {
    const orderRef = doc(db, 'orders', orderId);
    const orderSnapshot = await getDoc(orderRef);
    
    if (!orderSnapshot.exists()) {
      return null;
    }
    
    return { id: orderId, ...orderSnapshot.data() as Omit<Order, 'id'> };
  }
    // 更新訂單狀態
  async updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
    const orderRef = doc(db, 'orders', orderId);
    await updateDoc(orderRef, { status });
  }
}

export default new OrderService();
