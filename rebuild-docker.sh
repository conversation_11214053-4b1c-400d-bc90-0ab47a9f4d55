#!/bin/bash

# 快速重新構建 Docker 映像腳本
# 適用於開發階段快速重新部署

set -e

echo "=== 自然語言點餐系統 - 快速重新構建 ==="

# 檢查 Docker 是否運行
if ! docker info >/dev/null 2>&1; then
    echo "錯誤: Docker 未運行，請啟動 Docker"
    exit 1
fi

echo "1. 停止現有容器..."
docker-compose down

echo "2. 清理舊映像..."
docker rmi natural-order_natural-order 2>/dev/null || echo "舊映像不存在，跳過清理"

echo "3. 重新構建映像..."
docker-compose build --no-cache

echo "4. 啟動服務..."
docker-compose up -d

echo "5. 檢查服務狀態..."
sleep 5
docker-compose ps

echo ""
echo "✅ 重新構建完成！"
echo "🌐 訪問地址: http://localhost:3005"
echo "📋 會話測試頁面: http://localhost:3005/session-test.html"
echo ""
echo "查看日誌: docker-compose logs -f"
echo "停止服務: docker-compose down"
