# Gemini AI API 設定指南

## 問題描述

如果您在 Docker 部署後看到以下錯誤：
```
APPprompt 生成錯誤: Error: Gemini AI 未啟用，且已移除舊版NLP後備方案
```

這表示系統缺少有效的 Gemini AI API Key 配置。

## 解決步驟

### 1. 獲取 Gemini AI API Key

1. 前往 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 使用您的 Google 帳號登入
3. 點擊「Create API Key」
4. 選擇或創建一個 Google Cloud 項目
5. 複製生成的 API Key

### 2. 配置環境變數

#### 方法一：修改 .env 文件

1. 編輯項目根目錄的 `.env` 文件：
   ```bash
   # 找到以下行並替換為您的真實 API Key
   GEMINI_API_KEY=your-real-api-key-here
   USE_GEMINI_AI=true
   ```

2. 重啟 Docker 容器：
   ```bash
   docker-compose down
   docker-compose up -d
   ```

#### 方法二：使用環境變數

直接在啟動時設定：
```bash
GEMINI_API_KEY=your-real-api-key-here docker-compose up -d
```

### 3. 驗證配置

1. 檢查容器日誌：
   ```bash
   docker-compose logs natural-order-app
   ```

2. 應該看到類似以下的成功訊息：
   ```
   Gemini 服務已初始化，將使用動態生成的 APPprompt
   伺服器運行在端口 3000
   ```

3. 訪問應用並測試 APPprompt 生成功能

## 常見問題

### Q: API Key 無效
**A:** 確保您的 API Key 是從 Google AI Studio 獲取的，且沒有額外的空格或引號。

### Q: 配額限制
**A:** Gemini AI 有免費配額限制，如果超出可能需要升級到付費方案。

### Q: 網路連接問題
**A:** 確保 Docker 容器可以訪問外部網路，檢查防火牆設定。

## 安全注意事項

⚠️ **重要：**
- 不要將真實的 API Key 提交到版本控制系統
- 定期輪換您的 API Key
- 在生產環境中使用環境變數而非 .env 文件
- 監控 API 使用量避免意外費用

## 支援

如果仍有問題，請檢查：
1. [Google AI Studio 文檔](https://ai.google.dev/docs)
2. 項目的 [DOCKER_DEPLOYMENT.md](./DOCKER_DEPLOYMENT.md) 文件
3. Docker 容器日誌以獲取詳細錯誤信息