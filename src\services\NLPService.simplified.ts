import { GeminiService } from './GeminiService.js';
import fs from 'fs';
import path from 'path';

/**
 * 簡化的自然語言處理服務
 * 完全依賴 Gemini AI 處理，移除所有舊版文字比對和模糊匹配功能
 */
export class NLPService {
  private geminiService: GeminiService | null = null;
  
  constructor() {
    // 初始化 GeminiService（如果啟用 AI）
    if (process.env.USE_GEMINI_AI === 'true') {
      try {
        this.geminiService = new GeminiService();
      } catch (error) {
        console.error('[NLPService] 初始化 Gemini AI 服務失敗:', error);
      }
    }
  }

  /**
   * APPprompt 優先的點餐處理方法
   * 完全依賴 Gemini AI 處理，不再有後備方案
   * @param input 用戶輸入的自然語言文本
   */
  async processOrderWithAppPromptFirst(input: string): Promise<{
    success: boolean;
    useAI: boolean;
    data: any;
    analysis?: any;
    error?: string;
  }> {
    console.log('[NLPService] 開始 Gemini AI 處理:', input);
    
    try {
      // 1. 檢查是否有可用的 APPprompt
      const appPromptAvailable = await this.checkAppPromptAvailability();
      
      if (!appPromptAvailable) {
        return {
          success: false,
          useAI: false,
          data: { matches: [], unidentified: [input] },
          error: '沒有可用的 APPprompt，無法處理點餐請求',
          analysis: {
            aiProcessed: false,
            appPromptUsed: false,
            method: 'no_appprompt'
          }
        };
      }

      console.log('[NLPService] 發現可用的 APPprompt，使用 AI 處理');
      
      // 2. 使用 GeminiService 處理 APPprompt
      if (!this.geminiService) {
        throw new Error('Gemini AI 服務未初始化');
      }

      // 加載並使用 APPprompt
      const appPromptData = await this.loadLatestAppPrompt();
      if (!appPromptData) {
        throw new Error('無法加載 APPprompt 數據');
      }

      const aiResult = await this.geminiService.processNaturalLanguageOrder(input, null, appPromptData);
      
      // 🎯 直接返回 AI 自然語言回應，不再重新解析
      return {
        success: true,
        useAI: true,
        data: {
          matches: [], // 由於是自然語言回應，不提供結構化數據
          unidentified: [],
          aiResponse: aiResult, // 保存原始 AI 回應
          totalPrice: 0 // AI 回應中已包含價格信息
        },
        analysis: {
          entities: [],
          intent: "order",
          aiResponse: aiResult,
          aiProcessed: true,
          appPromptUsed: true,
          method: 'gemini_natural_response',
          message: 'Gemini AI 自然語言回應 - 無需額外解析'
        }
      };
      
    } catch (aiError: any) {
      console.error('[NLPService] AI 處理失敗:', aiError.message);
      
      return {
        success: false,
        useAI: true,
        data: { matches: [], unidentified: [input] },
        error: `AI 處理失敗: ${aiError.message}`,
        analysis: {
          aiProcessed: false,
          appPromptUsed: true,
          error: aiError.message
        }
      };
    }
  }

  /**
   * 檢查 APPprompt 是否可用
   */
  private async checkAppPromptAvailability(): Promise<boolean> {
    try {
      // 檢查最新的 APPprompt 文件
      const appPromptPattern = /^appPrompt_\d{12}\.json$/;
      const files = fs.readdirSync(process.cwd());
      const appPromptFiles = files.filter(file => appPromptPattern.test(file));
      
      if (appPromptFiles.length > 0) {
        // 找到最新的 APPprompt 文件
        const latestFile = appPromptFiles.sort().pop();
        const filePath = path.join(process.cwd(), latestFile!);
        
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8');
          const appPromptData = JSON.parse(content);
          
          // 檢查 APPprompt 是否包含菜單數據
          const hasMenuData = (appPromptData.menuData && 
                              appPromptData.menuData.categories && 
                              appPromptData.menuData.categories.length > 0) ||
                             (appPromptData.parameters && 
                              appPromptData.parameters.menu && 
                              appPromptData.parameters.menu.length > 0);
          
          console.log(`[NLPService] APPprompt 檢查結果 - 文件: ${latestFile}, 包含菜單: ${hasMenuData}`);
          return hasMenuData;
        }
      }
      
      console.log('[NLPService] 未找到有效的 APPprompt 文件');
      return false;
    } catch (error: any) {
      console.error('[NLPService] APPprompt 檢查失敗:', error.message);
      return false;
    }
  }

  /**
   * 載入最新的 APPprompt 數據
   */
  private async loadLatestAppPrompt(): Promise<string | null> {
    try {
      // 尋找最新的 APPprompt 文件
      const appPromptPattern = /^appPrompt_\d{12}\.json$/;
      const files = fs.readdirSync(process.cwd());
      const appPromptFiles = files.filter(file => appPromptPattern.test(file));
      
      if (appPromptFiles.length === 0) {
        console.log('[NLPService] 未找到 APPprompt 文件');
        return null;
      }
      
      // 獲取最新的文件（按文件名排序，最新的會在最後）
      const latestFile = appPromptFiles.sort().pop();
      const filePath = path.join(process.cwd(), latestFile!);
      
      console.log(`[NLPService] 載入 APPprompt 文件: ${latestFile}`);
      
      const content = fs.readFileSync(filePath, 'utf8');
      const appPromptData = JSON.parse(content);
      
      // 將 APPprompt 對象轉換為字符串，包含完整的系統提示和菜單信息
      return JSON.stringify(appPromptData);
    } catch (error: any) {
      console.error('[NLPService] 載入 APPprompt 失敗:', error.message);
      return null;
    }
  }
}
