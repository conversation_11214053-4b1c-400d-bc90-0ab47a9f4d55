/**
 * editor.js - 自然語言點餐系統的編輯器功能
 * 提供 BDD 和 AAprompt 編輯器的語法高亮和驗證功能
 */

// 引入 lodash 的 debounce 函數
// 確保不重複宣告 debounce
let editorDebounce;
if (typeof _ !== 'undefined') {
  editorDebounce = _.debounce;
} else {
  editorDebounce = function(func, wait) {
    let timeout;
    return function() {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, arguments), wait);
    };
  };
}

// 檢測是否為開發頁面
function isDevPage() {
  return window.location.pathname === '/dev' || window.location.pathname.endsWith('/dev');
}

// 初始化 CodeMirror 編輯器
let bddEditor, aaPromptEditor;

// 備用的getCurrentLanguage函數，如果語言資源文件還沒載入
function getCurrentLanguage() {
  // 直接使用localStorage實現，避免無限遞迴
  return localStorage.getItem('preferredLanguage') || 'zh-TW';
}

// 備用的getTranslation函數，如果語言資源文件還沒載入
function getTranslation(key) {
  // 檢查是否有語言資源對象
  if (typeof window.languageResources !== 'undefined' && window.languageResources) {
    const currentLanguage = getCurrentLanguage();
    if (window.languageResources[currentLanguage] && window.languageResources[currentLanguage][key]) {
      return window.languageResources[currentLanguage][key];
    }
    // 如果找不到翻譯，返回繁體中文版本或鍵值本身
    return (window.languageResources['zh-TW'] && window.languageResources['zh-TW'][key]) || key;
  }
  // 否則返回鍵值本身
  return key;
}

// BDD 關鍵字高亮規則
const bddKeywords = [
  "Feature:", "Scenario:", "Given", "When", "Then", "And", "But",
  "Background:", "Scenario Outline:", "Examples:"
];

// BDD 自定義模式
CodeMirror.defineMode("bdd", function() {
  return {
    token: function(stream) {
      if (stream.sol() && /Feature:|Scenario:|Background:|Scenario Outline:|Examples:/.test(stream.peek() + stream.match(/Feature:|Scenario:|Background:|Scenario Outline:|Examples:/, false))) {
        stream.match(/Feature:|Scenario:|Background:|Scenario Outline:|Examples:/);
        return "keyword";
      }
      
      if (stream.sol() && /Given|When|Then|And|But/.test(stream.peek() + stream.match(/Given|When|Then|And|But/, false))) {
        stream.match(/Given|When|Then|And|But/);
        return "atom";
      }
      
      // 字符串標記
      if (stream.match(/\"([^"]*)\"|\'([^']*)\'|`([^`]*)`/)) {
        return "string";
      }
      
      // 參數標記
      if (stream.match(/\$\w+/)) {
        return "variable";
      }
      
      // 跳過非關鍵字
      stream.next();
      return null;
    }
  };
});

// AAprompt 自定義模式
CodeMirror.defineMode("aaprompt", function() {
  return {
    token: function(stream) {
      // 標記角色和行為關鍵字
      if (stream.match(/作為|在|的情況下|，/)) {
        return "keyword";
      }
      
      // 標記變數和參數
      if (stream.match(/\{[^}]*\}/)) {
        return "variable";
      }
      
      // 跳過其他字符
      stream.next();
      return null;
    }
  };
});

// 載入BDD預設內容
async function loadBDDDefault(language) {
  try {
    console.log(`開始載入BDD預設內容，語言: ${language}`);
    const url = `/api/prompt/bdd-default?language=${language}`;
    console.log(`請求URL: ${url}`);

    const response = await fetch(url);
    console.log(`API回應狀態: ${response.status}`);

    const result = await response.json();
    console.log('API回應結果:', result);

    if (result.success) {
      console.log(`成功載入BDD預設內容，長度: ${result.content.length}`);
      console.log(`BDD預設內容前100字符: ${result.content.substring(0, 100)}`);
      return result.content;
    } else {
      console.error('載入BDD預設內容失敗:', result.error);
      return null;
    }
  } catch (error) {
    console.error('載入BDD預設內容錯誤:', error);
    return null;
  }
}

// 載入AAprompt預設內容
async function loadAAPromptDefault(language) {
  try {
    console.log(`開始載入AAprompt預設內容，語言: ${language}`);
    const url = `/api/prompt/aaprompt-default?language=${language}`;
    console.log(`請求URL: ${url}`);

    const response = await fetch(url);
    console.log(`API回應狀態: ${response.status}`);

    const result = await response.json();
    console.log('API回應結果:', result);

    if (result.success) {
      console.log(`成功載入AAprompt預設內容，長度: ${result.content.length}`);
      console.log(`AAprompt預設內容: ${result.content}`);
      return result.content;
    } else {
      console.error('載入AAprompt預設內容失敗:', result.error);
      return null;
    }
  } catch (error) {
    console.error('載入AAprompt預設內容錯誤:', error);
    return null;
  }
}

// 載入BDD預設內容到編輯器
async function loadBDDDefaultContent() {
  if (!bddEditor) {
    console.log('BDD編輯器尚未初始化');
    return;
  }

  // 檢查編輯器是否為空
  const currentContent = bddEditor.getValue().trim();
  if (currentContent) {
    console.log('BDD編輯器已有內容，跳過載入預設內容');
    return;
  }

  // 獲取當前語言
  const currentLanguage = getCurrentLanguage();
  console.log('載入BDD預設內容，語言:', currentLanguage);

  // 載入對應語言的預設內容
  const defaultContent = await loadBDDDefault(currentLanguage);
  if (defaultContent) {
    bddEditor.setValue(defaultContent);
    console.log('已載入BDD預設內容');
  }
}

// 強制載入BDD預設內容（不檢查是否為空）
async function forceLoadBDDDefaultContent() {
  if (!bddEditor) {
    console.log('BDD編輯器尚未初始化');
    return;
  }

  // 獲取當前語言
  const currentLanguage = getCurrentLanguage();
  console.log('強制載入BDD預設內容，語言:', currentLanguage);

  // 暫時顯示BDD編輯器標籤頁以確保編輯器可見
  const bddTab = document.getElementById('bdd-editor');
  const wasHidden = !bddTab.classList.contains('active');

  if (wasHidden) {
    bddTab.style.display = 'block';
  }

  // 載入對應語言的預設內容
  const defaultContent = await loadBDDDefault(currentLanguage);
  if (defaultContent) {
    bddEditor.setValue(defaultContent);
    // 刷新編輯器以確保內容正確顯示
    bddEditor.refresh();
    console.log('已強制載入BDD預設內容');
  }

  // 如果原本是隱藏的，恢復隱藏狀態
  if (wasHidden) {
    bddTab.style.display = '';
  }
}

// 載入AAprompt預設內容到編輯器
async function loadAAPromptDefaultContent() {
  if (!aaPromptEditor) {
    console.log('AAprompt編輯器尚未初始化');
    return;
  }

  // 檢查編輯器是否為空
  const currentContent = aaPromptEditor.getValue().trim();
  if (currentContent) {
    console.log('AAprompt編輯器已有內容，跳過載入預設內容');
    return;
  }

  // 獲取當前語言
  const currentLanguage = getCurrentLanguage();
  console.log('載入AAprompt預設內容，語言:', currentLanguage);

  // 載入對應語言的預設內容
  const defaultContent = await loadAAPromptDefault(currentLanguage);
  if (defaultContent) {
    aaPromptEditor.setValue(defaultContent);
    console.log('已載入AAprompt預設內容');
  }
}

// 強制載入AAprompt預設內容（不檢查是否為空）
async function forceLoadAAPromptDefaultContent() {
  if (!aaPromptEditor) {
    console.log('AAprompt編輯器尚未初始化');
    return;
  }

  // 獲取當前語言
  const currentLanguage = getCurrentLanguage();
  console.log('強制載入AAprompt預設內容，語言:', currentLanguage);

  // 暫時顯示AAprompt編輯器標籤頁以確保編輯器可見
  const aaTab = document.getElementById('aa-prompt-editor');
  const wasHidden = !aaTab.classList.contains('active');

  if (wasHidden) {
    aaTab.style.display = 'block';
  }

  // 載入對應語言的預設內容
  const defaultContent = await loadAAPromptDefault(currentLanguage);
  if (defaultContent) {
    aaPromptEditor.setValue(defaultContent);
    // 刷新編輯器以確保內容正確顯示
    aaPromptEditor.refresh();
    console.log('已強制載入AAprompt預設內容');
  }

  // 如果原本是隱藏的，恢復隱藏狀態
  if (wasHidden) {
    aaTab.style.display = '';
  }
}

// 檢查BDD內容是否為預設內容
async function isBDDDefaultContent(content, language) {
  const defaultContent = await loadBDDDefault(language);
  return defaultContent && content.trim() === defaultContent.trim();
}

// 檢查AAprompt內容是否為預設內容
async function isAAPromptDefaultContent(content, language) {
  const defaultContent = await loadAAPromptDefault(language);
  return defaultContent && content.trim() === defaultContent.trim();
}

// 獲取語言顯示名稱
function getLanguageDisplayName(language) {
  const languageNames = {
    'zh-TW': '繁體中文',
    'en-US': 'English',
    'ja-JP': '日本語'
  };
  return languageNames[language] || language;
}

// 強制刷新編輯器顯示 - 特別處理隱藏編輯器的內容更新
function forceRefreshEditor(editor) {
  if (!editor) return;

  const editorId = editor === window.bddEditor ? 'bdd' : 'aaprompt';
  console.log(`開始強制刷新${editorId}編輯器`);

  try {
    // 確保編輯器可見後再刷新
    const editorElement = editor.getWrapperElement();
    const wasHidden = editorElement.offsetParent === null;

    if (wasHidden) {
      console.log(`${editorId}編輯器當前隱藏，需要特殊處理`);

      // 獲取編輯器的父容器（標籤頁內容）
      const tabContent = editorElement.closest('.tab-content');
      if (tabContent) {
        // 暫時顯示標籤頁內容
        const originalDisplay = tabContent.style.display;
        const originalVisibility = tabContent.style.visibility;

        tabContent.style.display = 'block';
        tabContent.style.visibility = 'hidden';
        tabContent.style.position = 'absolute';
        tabContent.style.left = '-9999px';

        console.log(`${editorId}編輯器暫時顯示以進行內容更新`);

        // 強制刷新和重新渲染
        editor.refresh();
        editor.setSize();

        // 延遲恢復原始狀態
        setTimeout(() => {
          tabContent.style.display = originalDisplay;
          tabContent.style.visibility = originalVisibility;
          tabContent.style.position = '';
          tabContent.style.left = '';

          console.log(`${editorId}編輯器已恢復隱藏狀態，內容已更新`);
        }, 100);
      } else {
        // 回退到原來的方法
        const originalDisplay = editorElement.style.display;
        const originalVisibility = editorElement.style.visibility;

        editorElement.style.display = 'block';
        editorElement.style.visibility = 'hidden';

        editor.refresh();
        editor.setSize();

        setTimeout(() => {
          editorElement.style.display = originalDisplay;
          editorElement.style.visibility = originalVisibility;
          editor.refresh();
        }, 100);
      }

    } else {
      // 編輯器可見，直接刷新
      console.log(`${editorId}編輯器可見，直接刷新`);
      editor.refresh();
      editor.setSize();
    }

    console.log(`${editorId}編輯器強制刷新完成`);

  } catch (error) {
    console.log(`${editorId}編輯器刷新出錯:`, error);
  }
}

// 強制更新編輯器語言內容（不檢查是否為預設內容）
window.forceUpdateEditorsLanguage = async function forceUpdateEditorsLanguage(language) {
  console.log('強制更新所有編輯器語言內容到:', language);

  // 強制更新BDD編輯器
  if (window.bddEditor) {
    try {
      const defaultContent = await loadBDDDefault(language);
      if (defaultContent) {
        window.bddEditor.setValue(defaultContent);
        forceRefreshEditor(window.bddEditor);
        console.log('已強制更新BDD編輯器內容');
      }
    } catch (error) {
      console.error('強制更新BDD編輯器時發生錯誤:', error);
    }
  }

  // 強制更新AAprompt編輯器
  if (window.aaPromptEditor) {
    try {
      const defaultContent = await loadAAPromptDefault(language);
      if (defaultContent) {
        window.aaPromptEditor.setValue(defaultContent);
        forceRefreshEditor(window.aaPromptEditor);
        console.log('已強制更新AAprompt編輯器內容');
      }
    } catch (error) {
      console.error('強制更新AAprompt編輯器時發生錯誤:', error);
    }
  }
}

// 強制更新編輯器語言內容（語系切換時使用）- 簡化版本，直接載入預設內容
window.smartUpdateEditorsLanguage = async function smartUpdateEditorsLanguage(language) {
  console.log('=== 開始強制更新編輯器語言內容 ===');
  console.log('目標語言:', language);
  console.log('BDD編輯器存在:', !!window.bddEditor);
  console.log('AAprompt編輯器存在:', !!window.aaPromptEditor);

  // 強制更新BDD編輯器 - 直接載入預設內容，無條件更新
  if (window.bddEditor) {
    try {
      console.log('--- 開始強制更新BDD編輯器 ---');
      console.log('直接載入新語言的預設內容，無條件更新');

      const defaultContent = await loadBDDDefault(language);
      console.log('載入的BDD預設內容長度:', defaultContent ? defaultContent.length : 0);

      if (defaultContent) {
        // 直接設置內容，不做複雜的檢查
        window.bddEditor.setValue(defaultContent);
        forceRefreshEditor(window.bddEditor);
        console.log('✓ 已強制更新BDD編輯器內容');

        // 顯示更新成功訊息
        if (typeof showToastMessage === 'function' && !window.bddUpdateMessageShown) {
          const successMessage = getTranslation('refresh_success_bdd').replace('{language}', getLanguageDisplayName(language));
          showToastMessage(successMessage, 'success');
          window.bddUpdateMessageShown = true;
          setTimeout(() => {
            window.bddUpdateMessageShown = false;
          }, 3000);
        }
      } else {
        console.log('✗ 無法載入BDD預設內容');
      }
    } catch (error) {
      console.error('強制更新BDD編輯器時發生錯誤:', error);
    }
  } else {
    console.log('✗ BDD編輯器不存在');
  }

  // 強制更新AAprompt編輯器 - 直接載入預設內容，無條件更新
  if (window.aaPromptEditor) {
    try {
      console.log('--- 開始強制更新AAprompt編輯器 ---');
      console.log('直接載入新語言的預設內容，無條件更新');

      const defaultContent = await loadAAPromptDefault(language);
      console.log('載入的AAprompt預設內容長度:', defaultContent ? defaultContent.length : 0);

      if (defaultContent) {
        // 直接設置內容，不做複雜的檢查
        window.aaPromptEditor.setValue(defaultContent);
        forceRefreshEditor(window.aaPromptEditor);
        console.log('✓ 已強制更新AAprompt編輯器內容');

        // 顯示更新成功訊息 - 只在開發頁面顯示
        if (typeof showToastMessage === 'function' && !window.aaPromptUpdateMessageShown && isDevPage()) {
          const successMessage = getTranslation('refresh_success_aaprompt').replace('{language}', getLanguageDisplayName(language));
          showToastMessage(successMessage, 'success');
          window.aaPromptUpdateMessageShown = true;
          setTimeout(() => {
            window.aaPromptUpdateMessageShown = false;
          }, 3000);
        }
      } else {
        console.log('✗ 無法載入AAprompt預設內容');
      }
    } catch (error) {
      console.error('強制更新AAprompt編輯器時發生錯誤:', error);
    }
  } else {
    console.log('✗ AAprompt編輯器不存在');
  }

  console.log('=== 強制更新編輯器語言內容完成 ===');

  // 確保所有標籤頁的編輯器都能正確顯示
  setTimeout(() => {
    refreshAllEditorsDisplay();
  }, 500);

  // 設置標籤頁切換監聽器，確保隱藏的編輯器在顯示時能正確刷新
  setupTabSwitchListeners();
}

// 刷新所有編輯器的顯示
function refreshAllEditorsDisplay() {
  console.log('開始刷新所有編輯器顯示...');

  // 獲取當前激活的標籤頁
  const activeTab = document.querySelector('.tab-button.active');
  const currentActiveTab = activeTab ? activeTab.getAttribute('data-tab') : null;
  console.log('當前激活的標籤頁:', currentActiveTab);

  // 刷新BDD編輯器（兩次刷新確保內容更新）
  if (window.bddEditor) {
    try {
      window.bddEditor.refresh();
      console.log('BDD編輯器第一次刷新完成');

      setTimeout(() => {
        window.bddEditor.refresh();
        console.log('BDD編輯器第二次刷新完成');
      }, 50);
    } catch (error) {
      console.log('BDD編輯器刷新出錯:', error);
    }
  }

  // 刷新AAprompt編輯器（兩次刷新確保內容更新）
  if (window.aaPromptEditor) {
    try {
      window.aaPromptEditor.refresh();
      console.log('AAprompt編輯器第一次刷新完成');

      setTimeout(() => {
        window.aaPromptEditor.refresh();
        console.log('AAprompt編輯器第二次刷新完成');
      }, 100);
    } catch (error) {
      console.log('AAprompt編輯器刷新出錯:', error);
    }
  }

  console.log('所有編輯器刷新完成');
}

// 設置標籤頁切換監聽器
function setupTabSwitchListeners() {
  // 避免重複設置監聽器
  if (window.tabSwitchListenersSetup) {
    return;
  }

  console.log('設置標籤頁切換監聽器...');

  // 監聽所有標籤頁按鈕的點擊事件
  const tabButtons = document.querySelectorAll('.tab-button');

  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetTab = this.getAttribute('data-tab');
      console.log('標籤頁切換到:', targetTab);

      // 延遲刷新編輯器，確保標籤頁切換完成
      setTimeout(() => {
        if (targetTab === 'bdd-editor' && window.bddEditor) {
          console.log('刷新BDD編輯器（標籤頁切換）');
          window.bddEditor.refresh();
          window.bddEditor.setSize();

          // 額外的強制刷新
          setTimeout(() => {
            window.bddEditor.refresh();
          }, 50);

        } else if (targetTab === 'aa-prompt-editor' && window.aaPromptEditor) {
          console.log('刷新AAprompt編輯器（標籤頁切換）');

          // 多重刷新確保內容正確顯示
          window.aaPromptEditor.refresh();
          window.aaPromptEditor.setSize();

          setTimeout(() => {
            window.aaPromptEditor.refresh();
            console.log('AAprompt編輯器第二次刷新完成');
          }, 50);

          setTimeout(() => {
            window.aaPromptEditor.refresh();
            window.aaPromptEditor.setSize();
            console.log('AAprompt編輯器第三次刷新完成');
          }, 150);
        }
      }, 100);
    });
  });

  window.tabSwitchListenersSetup = true;
  console.log('標籤頁切換監聽器設置完成');
}

// 更新BDD編輯器語言內容
window.updateBDDEditorLanguage = async function updateBDDEditorLanguage(language) {
  if (!bddEditor) {
    console.log('BDD編輯器尚未初始化');
    return;
  }

  console.log('開始更新BDD編輯器語言內容:', language);

  const currentContent = bddEditor.getValue().trim();

  // 如果編輯器為空，直接載入新語言的預設內容
  if (!currentContent) {
    console.log('編輯器為空，載入新語言預設內容');
    const defaultContent = await loadBDDDefault(language);
    if (defaultContent) {
      bddEditor.setValue(defaultContent);
      // 使用強化的刷新機制
      forceRefreshEditor(bddEditor);
      console.log('已載入新語言的BDD預設內容:', language);
    }
    return;
  }

  // 更積極的自動更新策略：檢查當前內容是否為舊語言的預設內容
  const oldLanguages = ['zh-TW', 'en-US', 'ja-JP'];
  let isDefault = false;
  let matchedLanguage = null;

  console.log('檢查當前內容是否為預設內容...');
  for (const oldLang of oldLanguages) {
    if (oldLang !== language) {
      const isOldDefault = await isBDDDefaultContent(currentContent, oldLang);
      console.log(`檢查是否為 ${oldLang} 的預設內容:`, isOldDefault);
      if (isOldDefault) {
        isDefault = true;
        matchedLanguage = oldLang;
        break;
      }
    }
  }

  // 如果是預設內容，則自動替換為新語言的預設內容
  if (isDefault) {
    console.log(`當前內容是 ${matchedLanguage} 的預設內容，自動更新為新語言的預設內容`);
    const defaultContent = await loadBDDDefault(language);
    if (defaultContent) {
      bddEditor.setValue(defaultContent);
      // 使用強化的刷新機制
      forceRefreshEditor(bddEditor);
      console.log('已自動更新BDD編輯器為新語言的預設內容:', language);

      // 不顯示訊息，避免與智能更新重複
    }
  } else {
    console.log('BDD編輯器包含自定義內容');
    // 對於自定義內容，提供更寬鬆的自動更新條件
    if (currentContent.length < 1000) { // 對較短的內容（可能是簡單測試內容）自動更新
      console.log('內容較短，自動更新為新語言預設內容');
      const defaultContent = await loadBDDDefault(language);
      if (defaultContent) {
        bddEditor.setValue(defaultContent);
        forceRefreshEditor(bddEditor);
        console.log('已自動更新短內容的BDD編輯器為新語言的預設內容:', language);

        // 顯示自動更新成功訊息
        if (typeof showToastMessage === 'function') {
          const successMessage = getTranslation('refresh_success_bdd').replace('{language}', getLanguageDisplayName(language));
          showToastMessage(successMessage, 'success');
        }
      }
    } else if (currentContent.length < 5000) { // 對中等長度的內容（包括BDD預設內容）提供選項
      const confirmMessage = getTranslation('auto_switch_bdd_confirm').replace('{language}', getLanguageDisplayName(language));
      const shouldUpdate = confirm(confirmMessage);
      if (shouldUpdate) {
        const defaultContent = await loadBDDDefault(language);
        if (defaultContent) {
          bddEditor.setValue(defaultContent);
          forceRefreshEditor(bddEditor);
          console.log('用戶選擇更新BDD編輯器為新語言的預設內容:', language);
        }
      }
    } else {
      console.log('BDD編輯器包含較長的自定義內容，不自動更新');
    }
  }
}

// 更新AAprompt編輯器語言內容
window.updateAAPromptEditorLanguage = async function updateAAPromptEditorLanguage(language) {
  if (!aaPromptEditor) {
    console.log('AAprompt編輯器尚未初始化');
    return;
  }

  console.log('開始更新AAprompt編輯器語言內容:', language);

  const currentContent = aaPromptEditor.getValue().trim();

  // 如果編輯器為空，直接載入新語言的預設內容
  if (!currentContent) {
    console.log('AAprompt編輯器為空，載入新語言預設內容');
    const defaultContent = await loadAAPromptDefault(language);
    if (defaultContent) {
      aaPromptEditor.setValue(defaultContent);
      // 使用強化的刷新機制
      forceRefreshEditor(aaPromptEditor);
      console.log('已載入新語言的AAprompt預設內容:', language);
    }
    return;
  }

  // 更積極的自動更新策略：檢查當前內容是否為舊語言的預設內容
  const oldLanguages = ['zh-TW', 'en-US', 'ja-JP'];
  let isDefault = false;
  let matchedLanguage = null;

  console.log('檢查AAprompt當前內容是否為預設內容...');
  for (const oldLang of oldLanguages) {
    if (oldLang !== language) {
      const isOldDefault = await isAAPromptDefaultContent(currentContent, oldLang);
      console.log(`檢查AAprompt是否為 ${oldLang} 的預設內容:`, isOldDefault);
      if (isOldDefault) {
        isDefault = true;
        matchedLanguage = oldLang;
        break;
      }
    }
  }

  // 如果是預設內容，則自動替換為新語言的預設內容
  if (isDefault) {
    console.log(`AAprompt當前內容是 ${matchedLanguage} 的預設內容，自動更新為新語言的預設內容`);
    const defaultContent = await loadAAPromptDefault(language);
    if (defaultContent) {
      aaPromptEditor.setValue(defaultContent);
      // 使用強化的刷新機制
      forceRefreshEditor(aaPromptEditor);
      console.log('已自動更新AAprompt編輯器為新語言的預設內容:', language);

      // 顯示自動更新成功訊息 - 只在開發頁面顯示
      if (typeof showToastMessage === 'function' && isDevPage()) {
        const successMessage = getTranslation('refresh_success_aaprompt').replace('{language}', getLanguageDisplayName(language));
        showToastMessage(successMessage, 'success');
      }
    }
  } else {
    console.log('AAprompt編輯器包含自定義內容');
    // 對於自定義內容，提供更寬鬆的自動更新條件
    if (currentContent.length < 500) { // 對較短的內容（AAprompt預設內容通常較短）自動更新
      console.log('內容較短，自動更新為新語言預設內容');
      const defaultContent = await loadAAPromptDefault(language);
      if (defaultContent) {
        aaPromptEditor.setValue(defaultContent);
        forceRefreshEditor(aaPromptEditor);
        console.log('已自動更新短內容的AAprompt編輯器為新語言的預設內容:', language);

        // 顯示自動更新成功訊息 - 只在開發頁面顯示
        if (typeof showToastMessage === 'function' && isDevPage()) {
          const successMessage = getTranslation('refresh_success_aaprompt').replace('{language}', getLanguageDisplayName(language));
          showToastMessage(successMessage, 'success');
        }
      }
    } else if (currentContent.length < 2000) { // 對中等長度的內容提供選項
      const confirmMessage = getTranslation('auto_switch_aaprompt_confirm').replace('{language}', getLanguageDisplayName(language));
      const shouldUpdate = confirm(confirmMessage);
      if (shouldUpdate) {
        const defaultContent = await loadAAPromptDefault(language);
        if (defaultContent) {
          aaPromptEditor.setValue(defaultContent);
          forceRefreshEditor(aaPromptEditor);
          console.log('用戶選擇更新AAprompt編輯器為新語言的預設內容:', language);
        }
      }
    } else {
      console.log('AAprompt編輯器包含較長的自定義內容，不自動更新');
    }
  }
}

// 初始化編輯器
function initializeEditors() {
  // 初始化 BDD 編輯器
  bddEditor = CodeMirror.fromTextArea(document.getElementById('bdd-text'), {
    mode: "bdd",
    lineNumbers: true,
    theme: "material",
    lineWrapping: true,
    tabSize: 2,
    extraKeys: {"Ctrl-Space": "autocomplete"}
  });

  // 初始化 AAprompt 編輯器
  aaPromptEditor = CodeMirror.fromTextArea(document.getElementById('aa-text'), {
    mode: "aaprompt",
    lineNumbers: true,
    theme: "material",
    lineWrapping: true,
    tabSize: 2
  });

  // 將編輯器設置為全局變量，以便語系切換時可以訪問
  window.bddEditor = bddEditor;
  window.aaPromptEditor = aaPromptEditor;

  console.log('編輯器已初始化並設置為全局變量');
  console.log('BDD編輯器:', !!window.bddEditor);
  console.log('AAprompt編輯器:', !!window.aaPromptEditor);

  // 延遲載入預設內容，確保編輯器完全初始化
  setTimeout(() => {
    console.log('開始載入編輯器預設內容...');
    // 強制載入BDD預設內容（首次載入時）
    forceLoadBDDDefaultContent();

    // 強制載入AAprompt預設內容（首次載入時）
    forceLoadAAPromptDefaultContent();

    // 確保編輯器內容與當前語系一致
    setTimeout(() => {
      const currentLanguage = getCurrentLanguage();
      console.log('檢查編輯器內容是否與當前語系一致:', currentLanguage);
      if (typeof smartUpdateEditorsLanguage === 'function') {
        smartUpdateEditorsLanguage(currentLanguage);
      }
    }, 1000);
  }, 500);
  
  // 設置自動完成功能
  CodeMirror.commands.autocomplete = function(cm) {
    CodeMirror.showHint(cm, CodeMirror.hint.bdd);
  };
  
  // 為 BDD 編輯器添加自定義自動完成
  CodeMirror.registerHelper("hint", "bdd", function(editor) {
    const cur = editor.getCursor();
    const token = editor.getTokenAt(cur);
    const line = editor.getLine(cur.line);
    const start = token.start;
    const end = cur.ch;
    const prefix = line.slice(start, end);
    
    const list = bddKeywords.filter(k => k.startsWith(prefix));
    
    return {
      list: list,
      from: CodeMirror.Pos(cur.line, start),
      to: CodeMirror.Pos(cur.line, end)
    };
  });
  
  // 添加實時語法驗證
  bddEditor.on("change", editorDebounce(validateBDD, 500));
  aaPromptEditor.on("change", editorDebounce(validateAAPrompt, 500));
  
  // 綁定儲存按鈕的事件處理
  setupEditorSaveButtons();

  // 綁定語言刷新按鈕的事件處理
  setupLanguageRefreshButtons();
}

// 設置編輯器儲存按鈕功能
function setupEditorSaveButtons() {
  const saveBddBtn = document.getElementById('save-bdd');
  const saveAAPromptBtn = document.getElementById('save-aaprompt');
  
  if (saveBddBtn && bddEditor) {
    saveBddBtn.addEventListener('click', async function() {      try {
        const bddContent = bddEditor.getValue();
        if (!bddContent.trim()) {
          throw new Error(getTranslation('bdd_content_required'));
        }
        
        // 顯示儲存中狀態
        saveBddBtn.disabled = true;
        saveBddBtn.textContent = getTranslation('saving_bdd');
        
        console.log('正在儲存BDD內容...');
        
        // 發送儲存請求
        const response = await fetch('/api/prompt/save-bdd', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            bddText: bddContent
          })
        });
        
        if (!response.ok) {
          console.error('儲存請求失敗:', response.status, response.statusText);
          throw new Error(`儲存失敗 (${response.status}): ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('儲存BDD回應:', result);
          if (result.success) {
          showSuccess(getTranslation('bdd_save_success') || 'BDD 儲存成功！', 'bdd');
        } else {
          throw new Error(result.error || '儲存失敗');
        }      } catch (error) {
        console.error('儲存BDD錯誤:', error);
        showError(`${getTranslation('bdd_save_failed') || 'BDD 儲存失敗'}: ${error.message}`, 'bdd');
      }finally {
        saveBddBtn.disabled = false;
        saveBddBtn.textContent = getTranslation('save_bdd_btn');
      }
    });
    console.log('BDD編輯器儲存按鈕已綁定');
  }
  
  if (saveAAPromptBtn && aaPromptEditor) {
    saveAAPromptBtn.addEventListener('click', async function() {      try {
        const aaContent = aaPromptEditor.getValue();
        if (!aaContent.trim()) {
          throw new Error(getTranslation('aaprompt_content_required'));
        }
        
        // 顯示儲存中狀態
        saveAAPromptBtn.disabled = true;
        saveAAPromptBtn.textContent = getTranslation('saving_aaprompt');
        
        console.log('正在儲存AAprompt內容...');
        
        // 發送儲存請求
        const response = await fetch('/api/prompt/save-aaprompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            aaPromptText: aaContent
          })
        });
        
        if (!response.ok) {
          console.error('儲存請求失敗:', response.status, response.statusText);
          throw new Error(`儲存失敗 (${response.status}): ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('儲存AAprompt回應:', result);
          if (result.success) {
          showSuccess(getTranslation('aaprompt_save_success') || 'AAprompt 儲存成功！', 'aaprompt');
        } else {
          throw new Error(result.error || '儲存失敗');
        }      } catch (error) {
        console.error('儲存AAprompt錯誤:', error);
        showError(`${getTranslation('aaprompt_save_failed') || 'AAprompt 儲存失敗'}: ${error.message}`, 'aaprompt');
      }finally {
        saveAAPromptBtn.disabled = false;
        saveAAPromptBtn.textContent = getTranslation('save_aaprompt_btn');
      }
    });
    console.log('AAprompt編輯器儲存按鈕已綁定');
  }
}

// 設置語言刷新按鈕功能
function setupLanguageRefreshButtons() {
  const refreshBddBtn = document.getElementById('refresh-bdd-language');
  const refreshAAPromptBtn = document.getElementById('refresh-aaprompt-language');

  if (refreshBddBtn) {
    refreshBddBtn.addEventListener('click', async function() {
      try {
        const currentLanguage = getCurrentLanguage();
        console.log('手動刷新BDD編輯器語言內容到:', currentLanguage);

        // 顯示確認對話框
        const confirmMessage = getTranslation('confirm_refresh_bdd').replace('{language}', getLanguageDisplayName(currentLanguage));
        const shouldRefresh = confirm(confirmMessage);
        if (!shouldRefresh) {
          return;
        }

        // 顯示載入狀態
        refreshBddBtn.disabled = true;
        refreshBddBtn.textContent = getTranslation('refreshing');

        // 強制載入新語言的預設內容
        const defaultContent = await loadBDDDefault(currentLanguage);
        if (defaultContent && bddEditor) {
          bddEditor.setValue(defaultContent);
          forceRefreshEditor(bddEditor);
          console.log('手動刷新BDD編輯器內容完成');

          // 顯示成功訊息
          if (typeof showToastMessage === 'function') {
            const successMessage = getTranslation('refresh_success_bdd').replace('{language}', getLanguageDisplayName(currentLanguage));
            showToastMessage(successMessage, 'success');
          }
        } else {
          throw new Error(getTranslation('unable_load_default_content'));
        }
      } catch (error) {
        console.error('手動刷新BDD編輯器語言時發生錯誤:', error);
        if (typeof showToastMessage === 'function') {
          const errorMessage = `${getTranslation('refresh_failed_bdd')}: ${error.message}`;
          showToastMessage(errorMessage, 'error');
        }
      } finally {
        refreshBddBtn.disabled = false;
        refreshBddBtn.innerHTML = '🔄 ' + getTranslation('refresh_language_content');
      }
    });
    console.log('BDD語言刷新按鈕已綁定');
  }

  if (refreshAAPromptBtn) {
    refreshAAPromptBtn.addEventListener('click', async function() {
      try {
        const currentLanguage = getCurrentLanguage();
        console.log('手動刷新AAprompt編輯器語言內容到:', currentLanguage);

        // 顯示確認對話框
        const confirmMessage = getTranslation('confirm_refresh_aaprompt').replace('{language}', getLanguageDisplayName(currentLanguage));
        const shouldRefresh = confirm(confirmMessage);
        if (!shouldRefresh) {
          return;
        }

        // 顯示載入狀態
        refreshAAPromptBtn.disabled = true;
        refreshAAPromptBtn.textContent = getTranslation('refreshing');

        // 強制載入新語言的預設內容
        const defaultContent = await loadAAPromptDefault(currentLanguage);
        if (defaultContent && aaPromptEditor) {
          aaPromptEditor.setValue(defaultContent);
          forceRefreshEditor(aaPromptEditor);
          console.log('手動刷新AAprompt編輯器內容完成');

          // 顯示成功訊息 - 只在開發頁面顯示
          if (typeof showToastMessage === 'function' && isDevPage()) {
            const successMessage = getTranslation('refresh_success_aaprompt').replace('{language}', getLanguageDisplayName(currentLanguage));
            showToastMessage(successMessage, 'success');
          }
        } else {
          throw new Error(getTranslation('unable_load_default_content'));
        }
      } catch (error) {
        console.error('手動刷新AAprompt編輯器語言時發生錯誤:', error);
        if (typeof showToastMessage === 'function') {
          const errorMessage = `${getTranslation('refresh_failed_aaprompt')}: ${error.message}`;
          showToastMessage(errorMessage, 'error');
        }
      } finally {
        refreshAAPromptBtn.disabled = false;
        refreshAAPromptBtn.innerHTML = '🔄 ' + getTranslation('refresh_language_content');
      }
    });
    console.log('AAprompt語言刷新按鈕已綁定');
  }
}

// BDD 語法驗證
async function validateBDD() {
  const bddText = bddEditor.getValue();
  if (!bddText.trim()) return;
  
  try {
    const response = await fetch('/api/prompt/validate-bdd', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ bddText })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const validation = result.validation;
      
      if (!validation.valid) {
        showBDDErrors(validation.errors);
      } else {
        clearBDDErrors();
      }
    } else {
      console.error('BDD 驗證失敗:', result.error);
    }
  } catch (error) {
    console.error('BDD 驗證錯誤:', error);
  }
}

// 顯示 BDD 語法錯誤
function showBDDErrors(errors) {
  clearBDDErrors();
  
  const bddContainer = bddEditor.getWrapperElement();
  const errorDiv = document.createElement('div');
  errorDiv.className = 'editor-error';
  errorDiv.style.color = '#ff5252';
  errorDiv.style.padding = '10px';
  errorDiv.style.backgroundColor = 'rgba(255, 82, 82, 0.1)';
  errorDiv.style.marginTop = '10px';
  errorDiv.style.borderRadius = '4px';
  
  let errorContent = `<strong>${getTranslation('bdd_syntax_error') || 'BDD 語法錯誤'}:</strong><ul>`;
  errors.forEach(err => {
    errorContent += `<li>${err}</li>`;
  });
  errorContent += '</ul>';
  
  errorDiv.innerHTML = errorContent;
  bddContainer.parentNode.insertBefore(errorDiv, bddContainer.nextSibling);
}

// 清除 BDD 錯誤顯示
function clearBDDErrors() {
  const errorDivs = document.querySelectorAll('.editor-error');
  errorDivs.forEach(div => div.remove());
}

// AAprompt 語法驗證
async function validateAAPrompt() {
  const aaText = aaPromptEditor.getValue();
  if (!aaText.trim()) return;
  
  try {
    const response = await fetch('/api/prompt/validate-aaprompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ aaText })
    });
    
    const result = await response.json();
    
    if (result.success) {
      const validation = result.validation;
      
      if (!validation.valid) {
        showAAPromptErrors(validation.errors);
      } else {
        clearAAPromptErrors();
      }
    } else {
      console.error('AAprompt 驗證失敗:', result.error);
    }
  } catch (error) {
    console.error('AAprompt 驗證錯誤:', error);
  }
}

// 顯示 AAprompt 語法錯誤
function showAAPromptErrors(errors) {
  clearAAPromptErrors();
  
  const aaContainer = aaPromptEditor.getWrapperElement();
  const errorDiv = document.createElement('div');
  errorDiv.className = 'editor-error-aa';
  errorDiv.style.color = '#ff5252';
  errorDiv.style.padding = '10px';
  errorDiv.style.backgroundColor = 'rgba(255, 82, 82, 0.1)';
  errorDiv.style.marginTop = '10px';
  errorDiv.style.borderRadius = '4px';
  
  let errorContent = `<strong>${getTranslation('aaprompt_syntax_error')}:</strong><ul>`;
  errors.forEach(err => {
    errorContent += `<li>${err}</li>`;
  });
  errorContent += '</ul>';
  
  errorDiv.innerHTML = errorContent;
  aaContainer.parentNode.insertBefore(errorDiv, aaContainer.nextSibling);
}

// 清除 AAprompt 錯誤顯示
function clearAAPromptErrors() {
  const errorDivs = document.querySelectorAll('.editor-error-aa');
  errorDivs.forEach(div => div.remove());
}

// 更新生成狀態顯示
function updateGenerateStatus(isGenerating) {
  const generateBtn = document.getElementById('generate-btn');
  const apppromptContainer = document.getElementById('appprompt-container');
  const generateError = document.getElementById('generate-error');
  
  if (generateBtn) {
    generateBtn.disabled = isGenerating;
    generateBtn.textContent = isGenerating ? getTranslation('generating') || '生成中...' : getTranslation('generate_appprompt') || '生成 APPprompt';
  }
  
  if (generateError) {
    generateError.style.display = 'none';
  }
  
  // 如果正在生成，顯示一個加載指示器
  if (isGenerating && apppromptContainer) {
    apppromptContainer.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p class="generating-text">${getTranslation('generating_appprompt_wait') || '正在生成 APPprompt，請稍候...'}</p>
      </div>
    `;
  }
}

// APPPrompt 生成處理（支援會話隔離）
async function generateAPPPrompt() {
  try {
    updateGenerateStatus(true);

    // 檢查會話管理器是否可用
    if (!window.sessionManager) {
      throw new Error('會話管理器未初始化');
    }

    // 檢查編輯器是否初始化
    if (!bddEditor || !aaPromptEditor) {
      throw new Error(getTranslation('editor_not_initialized') || '編輯器尚未初始化，請先切換到 BDD 或 AAprompt 編輯器標籤頁');
    }

    // 獲取編輯器內容
    const bddContent = bddEditor.getValue().trim();
    const aaContent = aaPromptEditor.getValue().trim();

    // 檢查是否有內容可以生成
    if (!bddContent && !aaContent) {
      throw new Error(getTranslation('no_content_to_generate') || '請先在 BDD 或 AAprompt 編輯器中輸入內容');
    }
    
    // 優先使用 BDD 內容，其次使用 AAprompt 內容
    const useType = bddContent ? 'bdd' : 'aaprompt';
    const content = bddContent || aaContent;
    // 使用會話管理器生成 APPprompt
    console.log(`${getTranslation('generating_appprompt_using') || '正在生成 APPprompt，使用'} ${useType} ${getTranslation('content') || '內容'}`);

    const currentLanguage = getCurrentLanguage() || 'zh-TW';
    const result = await window.sessionManager.generateAppPrompt(useType, content, currentLanguage);

    // 現在只在APPprompt生成器頁籤中顯示結果
    const container = document.getElementById('appprompt-container');
    if (container) {
      // 使用 showGeneratedAPPPrompt 函數顯示完整的 APPprompt 結果
      showGeneratedAPPPrompt(result, 'appprompt-container');
      showSuccess(getTranslation('appprompt_generation_success') || 'APPPrompt 生成成功！');

      // 更新會話狀態
      if (typeof updateSessionStatus === 'function') {
        updateSessionStatus();
      }
    }
    
  } catch (error) {
    console.error(getTranslation('generation_error') || '生成錯誤:', error);
    showError(`${getTranslation('appprompt_generation_error') || '生成APPprompt錯誤'}: ${error.message}`);
  } finally {
    updateGenerateStatus(false);
  }
}

// 生成 APPprompt 的狀態管理
let isGenerating = false;

function setGeneratingState(generating, message = getTranslation('generating_with_gemini') || '正在使用 USI AIOS 生成 APPprompt...') {
  isGenerating = generating;
  const generateBtn = document.getElementById('generate-btn');
  const resultDisplay = document.getElementById('app-prompt-result');
  
  if (generating) {
    generateBtn.disabled = true;
    generateBtn.classList.add('disabled');
    resultDisplay.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p>${message}</p>
      </div>
    `;
  } else {
    generateBtn.disabled = false;
    generateBtn.classList.remove('disabled');
  }
}

// 處理生成APPprompt請求
async function processGenerateRequest(url, data) {
  // 取得結果顯示容器
  const container = document.getElementById('result-container');
  const promptContainer = document.getElementById('appprompt-container');
  
  try {
    // 顯示生成中狀態
    promptContainer.innerHTML = `
      <div class="generating-indicator">
        <div class="spinner"></div>
        <p class="generating-text">${getTranslation('analyzing_with_gemini') || '正在使用 USI AIOS 分析並生成 APPprompt...'}</p>
      </div>
    `;
    
    const generateBtn = document.getElementById('generate-btn');
    if (generateBtn) {
      generateBtn.disabled = true;
    }
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    
    if (generateBtn) {
      generateBtn.disabled = false;
    }
    
    if (result.success) {
      const aiGenerated = result.appPrompt.metadata.aiGenerated;
      showGeneratedAPPPrompt(result.appPrompt);
      showSuccess(aiGenerated ? getTranslation('gemini_generation_success') || 'USI AIOS 已成功生成 APPprompt' : getTranslation('appprompt_generation_success') || 'APPprompt 已成功生成');
    } else {
      showError(getTranslation('appprompt_generation_failed') || '生成 APPprompt 失敗: ' + result.error);
    }
  } catch (error) {
    if (generateBtn) {
      generateBtn.disabled = false;
    }
    showError(getTranslation('generation_process_error') || '生成過程中發生錯誤: ' + error.message);
  }
}

// 顯示模擬的APPprompt結果
function showMockAppPrompt() {
  // 更改為實際產生的結果結構
  const appPrompt = {
    prompt: `// 根據 Actor-Action 分析自然語言點餐需求
const processOrder = async (input, context) => {
  // 自然語言處理模組
  const nlpResult = await nlpModule.processInput(input);
  
  // 查詢模組
  const menuItems = await queryModule.findMenuItems(nlpResult.entities);
  
  // 回應生成模組
  const response = responseModule.generateResponse(menuItems, nlpResult);
  
  return response;
}`,
    parameters: {
      "restaurant_id": "mcdonalds", 
      "language": "zh-TW",
      "order_type": "dine-in",
      "recognition_confidence": 0.8
    },
    metadata: {
      "model": "natural-order-v1",
      "version": "1.0.0",
      "created_at": new Date().toISOString()
    }  };
  
  showGeneratedAPPPrompt(appPrompt, 'appprompt-container');
  
  // 同時顯示程式碼預覽
  const container = document.getElementById(containerId);
  container.innerHTML += `
    <h3 style="margin-top: 30px;">程式碼實現示例</h3>
    <div class="app-prompt-result">
      <h4>自然語言處理模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockNlpModule(), {language: 'javascript'}).value}</code></pre>
      
      <h4>查詢模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockQueryModule(), {language: 'javascript'}).value}</code></pre>
      
      <h4>回應生成模組</h4>
      <pre><code class="language-javascript">${hljs.highlight(generateMockResponseModule(), {language: 'javascript'}).value}</code></pre>
    </div>
  `;
}

// 顯示生成的 APPprompt
function showGeneratedAPPPrompt(appPrompt, containerId = 'appprompt-container') {
  const container = document.getElementById(containerId);
  
  if (!appPrompt) {
    container.innerHTML = `<div class="alert alert-warning">${getTranslation('generated_appprompt_empty') || '生成的 APPprompt 為空'}</div>`;
    return;
  }
  
  let html = `<h3>${getTranslation('generated_appprompt') || '生成的 APPprompt'}</h3>`;
  html += '<div class="app-prompt-result">';
  
  try {
    // 檢查 hljs (highlight.js) 是否可用
    const hljs = window.hljs || { highlight: (code, opts) => ({ value: code }) };
    
    // 格式化提示詞
    html += `<h4>${getTranslation('prompt') || '提示詞'}:</h4>`;
    if (appPrompt.prompt) {
      try {
        html += `<pre><code class="language-javascript">${hljs.highlight(appPrompt.prompt, {language: 'javascript'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${appPrompt.prompt}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_prompt_content') || '無提示詞內容'}</p>`;
    }
    
    // 格式化參數
    html += `<h4>${getTranslation('parameters') || '參數'}:</h4>`;
    if (appPrompt.parameters) {
      try {
        const jsonParams = JSON.stringify(appPrompt.parameters, null, 2);
        html += `<pre><code class="language-json">${hljs.highlight(jsonParams, {language: 'json'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${JSON.stringify(appPrompt.parameters, null, 2)}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_parameters_content') || '無參數內容'}</p>`;
    }
    
    // 格式化元數據
    html += `<h4>${getTranslation('metadata') || '元數據'}:</h4>`;
    if (appPrompt.metadata) {
      try {
        const jsonMeta = JSON.stringify(appPrompt.metadata, null, 2);
        html += `<pre><code class="language-json">${hljs.highlight(jsonMeta, {language: 'json'}).value}</code></pre>`;
      } catch (e) {
        html += `<pre><code>${JSON.stringify(appPrompt.metadata, null, 2)}</code></pre>`;
      }
    } else {
      html += `<p>${getTranslation('no_metadata_content') || '無元數據內容'}</p>`;
    }    html += '</div>';
    container.innerHTML = html;
    
    // 顯示主下載按鈕
    const downloadBtn = document.getElementById('download-btn');
    if (downloadBtn) {
      downloadBtn.style.display = 'inline-block';
    }
    
    // 嘗試自動套用 highlight.js 格式化
    try {
      if (typeof hljs.highlightAll === 'function') {
        hljs.highlightAll();
      } else if (container.querySelectorAll) {
        container.querySelectorAll('pre code').forEach(block => {
          try {
            hljs.highlightElement(block);
          } catch (e) {
            console.warn(getTranslation('code_highlight_failed') || '代碼高亮失敗:', e);
          }
        });
      }
    } catch (e) {
      console.warn(getTranslation('code_highlight_process_failed') || '代碼高亮處理失敗:', e);
    }
  } catch (error) {
    console.error(getTranslation('display_appprompt_error') || '顯示 APPprompt 錯誤:', error);
    container.innerHTML = `<div class="alert alert-danger">${getTranslation('display_appprompt_error_occurred') || '顯示 APPprompt 時發生錯誤'}: ${error.message}</div>`;
  }
    // 保存生成的APPprompt，以便下載
  window.generatedAPPPrompt = appPrompt;
}

// 下載 APPprompt
function downloadAPPPrompt() {
  if (!window.generatedAPPPrompt) {
    showError(getTranslation('generate_appprompt_first') || '請先生成 APPprompt');
    return;
  }
  
  const appPrompt = window.generatedAPPPrompt;
  
  // 將APPprompt格式化為JSON字符串
  const jsonContent = JSON.stringify({
    prompt: appPrompt.prompt,
    parameters: appPrompt.parameters,
    metadata: appPrompt.metadata
  }, null, 2);
  
  // 創建Blob對象
  const blob = new Blob([jsonContent], { type: 'application/json' });
  
  // 創建URL
  const url = URL.createObjectURL(blob);
  
  // 創建下載鏈接
  const downloadLink = document.createElement('a');
  downloadLink.href = url;
    // 使用當前本地日期和時間作為文件名 (依據用戶電腦的時區)
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}`;
  
  downloadLink.download = `appPrompt_${timestamp}.json`;
  
  // 模擬點擊
  document.body.appendChild(downloadLink);
  downloadLink.click();
  
  // 清理
  document.body.removeChild(downloadLink);
  URL.revokeObjectURL(url);
  
  showSuccess(getTranslation('appprompt_download_success') || 'APPprompt 已成功下載');
}

// 圖片載入和base64編碼處理
function handleImageUpload(files) {
  if (!files || files.length === 0) {
    console.warn(getTranslation('no_file_selected') || '未選擇檔案');
    return;
  }

  const file = files[0];
  if (!file.type.match('image.*')) {
    showError(getTranslation('please_select_image') || '請選擇圖片檔案');
    return;
  }

  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const base64Image = e.target.result;
      if (!base64Image.startsWith('data:image/')) {
        showError(getTranslation('invalid_image_format') || '圖片格式無效');
        return;
      }
      document.getElementById('preview-image').src = base64Image;
      document.getElementById('preview-image').style.display = 'block';
    } catch (error) {
      console.error(getTranslation('image_processing_error') || '圖片處理錯誤:', error);
      showError(getTranslation('image_processing_failed') || '圖片處理失敗');
    }
  };
  
  reader.onerror = function() {
    console.error(getTranslation('file_read_error') || '檔案讀取錯誤');
    showError(getTranslation('file_read_failed') || '檔案讀取失敗');
  };

  reader.readAsDataURL(file);
}

// 初始化拖放區域
function initializeDropArea() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const browseBtn = document.getElementById('browse-btn');
    
    if (dropArea && fileInput) {
        // 檢查是否已經初始化過，防止重複綁定事件
        if (dropArea.dataset.initialized === 'true') {
            console.log(getTranslation('drop_area_already_initialized') || '拖放區域已經初始化過，跳過');
            return;
        }
        
        // 標記為已初始化
        dropArea.dataset.initialized = 'true';
        
        // 拖放效果
        dropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#4CAF50';
        });
        
        dropArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#ddd';
        });
        
        dropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            dropArea.style.borderColor = '#ddd';
            console.log(getTranslation('file_drop_triggered') || '檔案拖放事件觸發');
            
            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                const file = e.dataTransfer.files[0];
                // 直接設置檔案到 input
                updateFileInput(fileInput, e.dataTransfer.files);
                // 更新顯示
                const selectedFileText = getTranslation('file_selected') || '已選擇檔案';
                const fileSizeText = getTranslation('file_size') || '檔案大小';
                dropArea.innerHTML = `<p>${selectedFileText}：${file.name}</p><p class="small">${fileSizeText}：${formatFileSize(file.size)}</p>`;
                
                // 清除任何錯誤訊息
                const errorMessage = document.getElementById('error-message');
                if (errorMessage) {
                    errorMessage.style.display = 'none';
                }
                
                // 啟用上傳按鈕
                const uploadBtn = document.getElementById('upload-btn');
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
                
                // 不要自動提交
            }        });
        
        // 點擊拖放區域，保留這個事件，但不阻止冒泡，允許事件傳遞到file-input
        console.log(getTranslation('ensure_drop_area_click') || '確保拖放區域點擊事件能觸發檔案選擇對話框');
          // 檔案選擇變更
        fileInput.addEventListener('change', (e) => {
            console.log(getTranslation('file_selection_triggered') || '檔案選擇事件觸發');
            if (e.target && e.target.files && e.target.files.length > 0) {
                const file = e.target.files[0];
                // 更新顯示
                const selectedFileText = getTranslation('file_selected') || '已選擇檔案';
                const fileSizeText = getTranslation('file_size') || '檔案大小';
                dropArea.innerHTML = `<p>${selectedFileText}：${file.name}</p><p class="small">${fileSizeText}：${formatFileSize(file.size)}</p>`;
                
                // 清除任何錯誤訊息
                const errorMessage = document.getElementById('error-message');
                if (errorMessage) {
                    errorMessage.style.display = 'none';
                }
                
                // 顯示成功消息
                const fileSelectedSuccessMsg = getTranslation('file_selected_success') || '檔案已成功選擇，請點擊「上傳菜單」按鈕完成上傳';
                showSuccess(fileSelectedSuccessMsg);
                
                // 啟用上傳按鈕
                const uploadBtn = document.getElementById('upload-btn');
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
            }
        });
    }
    
    // 確保瀏覽按鈕能觸發檔案選擇
    if (browseBtn && fileInput) {
        browseBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log(getTranslation('browse_button_clicked') || '點擊瀏覽按鈕，觸發檔案選擇對話框');
            fileInput.click();
        });
    }
}

// 格式化檔案大小（KB, MB等）
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
}

// 輔助函數：更新檔案輸入
function updateFileInput(fileInput, files) {
    try {
        // 建立一個新的 DataTransfer 物件
        const dataTransfer = new DataTransfer();
        // 新增檔案
        for (let i = 0; i < files.length; i++) {
            dataTransfer.items.add(files[i]);
        }
        // 設置檔案
        fileInput.files = dataTransfer.files;
    } catch (error) {
        console.error(getTranslation('set_file_failed') || '設置檔案失敗:', error);
    }
}

// 初始化圖片上傳
function initializeImageUpload() {
    const imageInput = document.getElementById('image-upload');
    if (imageInput) {
        imageInput.addEventListener('change', function(event) {
            handleImageUpload(event.target.files);
        });
    }
}

// 顯示錯誤訊息（與 index.html 中兼容）
function showError(message, context) {
    // 根據當前活躍的標籤頁選擇合適的消息元素
    let errorDiv = null;
    let errorContent = null;
    
    // 確定當前處於哪個標籤頁
    const activeTab = document.querySelector('.tab.active');
    let tabId = activeTab ? activeTab.dataset.tab : '';
    
    // 如果提供了上下文，使用它來確定要使用哪個消息元素
    if (context === 'bdd') {
        errorDiv = document.getElementById('bdd-error-message');
        errorContent = document.getElementById('bdd-error-content');
    } else if (context === 'aaprompt') {
        errorDiv = document.getElementById('aa-error-message');
        errorContent = document.getElementById('aa-error-content');
    } else if (tabId === 'bdd-editor') {
        errorDiv = document.getElementById('bdd-error-message');
        errorContent = document.getElementById('bdd-error-content');
    } else if (tabId === 'aa-prompt-editor') {
        errorDiv = document.getElementById('aa-error-message');
        errorContent = document.getElementById('aa-error-content');
    } else {
        // 默認使用 menu-management 的消息元素
        errorDiv = document.getElementById('error-message');
        errorContent = document.getElementById('error-content');
    }
    
    // 如果找不到特定元素，則使用通用的 error-message
    if (!errorDiv) {
        errorDiv = document.getElementById('error-message');
        errorContent = document.getElementById('error-content');
    }
    
    if (errorDiv) {
        // 如果找到error-content元素，設置其內容，否則設置整個errorDiv的內容
        if (errorContent) {
            errorContent.textContent = message;
        } else {
            errorDiv.innerHTML = `<strong><i class="fas fa-exclamation-circle"></i> ${getTranslation('error') || '錯誤'}！</strong> ${message}`;
        }
        
        // 顯示並設置淡出效果
        errorDiv.style.display = 'block';
        errorDiv.style.opacity = '1';
        errorDiv.style.transition = 'opacity 0.5s ease-in-out';
        
        // 如果已有計時器，清除它
        if (errorDiv._timeoutId) {
            clearTimeout(errorDiv._timeoutId);
        }
        
        // 設置新計時器，5秒後淡出
        errorDiv._timeoutId = setTimeout(() => {
            errorDiv.style.opacity = '0';
            setTimeout(() => {
                errorDiv.style.display = 'none';
                errorDiv.style.opacity = '1'; // 重置為下次顯示做準備
            }, 500); // 0.5秒後完全隱藏
        }, 5000);
        
        // 同時顯示一個浮動提示（如果存在）
        if (typeof showToastMessage === 'function') {
            showToastMessage(`<strong>${getTranslation('error') || '錯誤'}</strong><br>${message}`, 'error');
        }
    }
    console.error(getTranslation('error') || '錯誤:', message);
}

// 顯示成功訊息（與 index.html 中兼容）
function showSuccess(message, context) {
    // 根據當前活躍的標籤頁選擇合適的消息元素
    let successDiv = null;
    let successContent = null;
    
    // 確定當前處於哪個標籤頁
    const activeTab = document.querySelector('.tab.active');
    let tabId = activeTab ? activeTab.dataset.tab : '';
    
    // 如果提供了上下文，使用它來確定要使用哪個消息元素
    if (context === 'bdd') {
        successDiv = document.getElementById('bdd-success-message');
        successContent = document.getElementById('bdd-success-content');
    } else if (context === 'aaprompt') {
        successDiv = document.getElementById('aa-success-message');
        successContent = document.getElementById('aa-success-content');
    } else if (tabId === 'bdd-editor') {
        successDiv = document.getElementById('bdd-success-message');
        successContent = document.getElementById('bdd-success-content');
    } else if (tabId === 'aa-prompt-editor') {
        successDiv = document.getElementById('aa-success-message');
        successContent = document.getElementById('aa-success-content');
    } else {
        // 默認使用 menu-management 的消息元素
        successDiv = document.getElementById('success-message');
        successContent = document.getElementById('success-content');
    }
    
    // 如果找不到特定元素，則使用通用的 success-message
    if (!successDiv) {
        successDiv = document.getElementById('success-message');
        successContent = document.getElementById('success-content');
    }
    
    if (successDiv) {
        // 如果找到success-content元素，設置其內容，否則設置整個successDiv的內容
        if (successContent) {
            successContent.textContent = message;
        } else {
            successDiv.innerHTML = `<strong><i class="fas fa-check-circle"></i> ${getTranslation('success') || '成功'}！</strong> ${message}`;
        }
        
        // 顯示並設置淡出效果
        successDiv.style.display = 'block';
        successDiv.style.opacity = '1';
        successDiv.style.transition = 'opacity 0.5s ease-in-out';
        
        // 如果已有計時器，清除它
        if (successDiv._timeoutId) {
            clearTimeout(successDiv._timeoutId);
        }
        
        // 設置新計時器，5秒後淡出
        successDiv._timeoutId = setTimeout(() => {
            successDiv.style.opacity = '0';
            setTimeout(() => {
                successDiv.style.display = 'none';
                successDiv.style.opacity = '1'; // 重置為下次顯示做準備
            }, 500); // 0.5秒後完全隱藏
        }, 5000);
        
    }
    console.log(getTranslation('success') || '成功:', message);
}

// 更新預覽區域（與 index.html 中兼容）
function updatePreview(data) {
    const previewContainer = document.getElementById('preview-container');
    if (!previewContainer || !data) return;

    // 預覽邏輯，與 index.html 保持一致
    console.log(getTranslation('update_preview') || '更新預覽:', data);
}

// 在 DOMContentLoaded 時初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(getTranslation('initializing_editor_upload') || '初始化編輯器和上傳功能...');

    // 延遲初始化編輯器，確保DOM完全載入
    setTimeout(() => {
        // 初始化編輯器
        if (typeof initializeEditors === 'function') {
            initializeEditors();
        }

        // 設置標籤頁切換監聽器
        if (typeof setupTabSwitchListeners === 'function') {
            setupTabSwitchListeners();
        }
    }, 200);

    // 初始化拖放區域
    initializeDropArea();

    // 初始化圖片上傳
    initializeImageUpload();      // 綁定 APPprompt 生成按鈕點擊事件
    const generateBtn = document.getElementById('generate-btn');
    if (generateBtn) {
        console.log(getTranslation('found_generate_btn') || '找到 generate-btn 按鈕，綁定點擊事件');
        generateBtn.addEventListener('click', generateAPPPrompt);
    } else {
        console.error(getTranslation('generate_btn_not_found') || '找不到 generate-btn 按鈕');
    }
    
    // 綁定主下載按鈕的事件處理
    const downloadBtn = document.getElementById('download-btn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadAPPPrompt);
    }
    
    // 不再嘗試調用setupUploadButton，因為它在index.html中定義
    // 而不是在editor.js中
    
    // 進行一些診斷日誌
    const uploadBtn = document.getElementById('upload-btn');
    const fileInput = document.getElementById('file-input');
    const dropArea = document.getElementById('drop-area');
    
    console.log(getTranslation('upload_btn_exists') || '上傳按鈕存在:', !!uploadBtn);
    console.log(getTranslation('file_input_exists') || '檔案輸入框存在:', !!fileInput);
    console.log(getTranslation('drop_area_exists') || '拖放區域存在:', !!dropArea);
});
