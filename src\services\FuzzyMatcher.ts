// 模糊匹配服務，用於提高自然語言點餐系統中菜單項目識別的準確性
export class FuzzyMatcher {
  /**
   * 計算兩個字符串之間的萊文斯坦距離（編輯距離）
   * @param a 第一個字符串
   * @param b 第二個字符串
   * @returns 編輯距離
   */
  levenshteinDistance(a: string, b: string): number {
    const matrix: number[][] = [];

    // 初始化矩陣
    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }

    // 填充矩陣
    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        if (b.charAt(i-1) === a.charAt(j-1)) {
          matrix[i][j] = matrix[i-1][j-1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i-1][j-1] + 1, // 替換
            matrix[i][j-1] + 1,   // 插入
            matrix[i-1][j] + 1    // 刪除
          );
        }
      }
    }
    
    return matrix[b.length][a.length];
  }

  /**
   * 計算字符串相似度（0-1之間，1表示完全相同）
   * @param a 第一個字符串
   * @param b 第二個字符串
   * @returns 相似度
   */
  stringSimilarity(a: string, b: string): number {
    if (!a || !b) return 0;
    if (a === b) return 1.0;
    
    const distance = this.levenshteinDistance(a, b);
    const maxLength = Math.max(a.length, b.length);
    
    return 1 - distance / maxLength;
  }

  /**
   * 檢查一個字符串是否包含另一個字符串（考慮不同的形式和拼寫變體）
   * @param text 待檢測文本
   * @param term 要查找的詞項
   * @param threshold 最低相似度閾值
   * @returns 是否包含 
   */
  contains(text: string, term: string, threshold = 0.7): boolean {
    if (text.includes(term)) return true;
    
    const words = text.split(/\s+/);
    for (const word of words) {
      const similarity = this.stringSimilarity(word, term);
      if (similarity >= threshold) {
        return true;
      }
    }
    
    return false;
  }
  /**
   * 將中文數字轉換為阿拉伯數字
   * @param text 包含中文數字的文本
   * @returns 轉換後的阿拉伯數字，如果無法轉換則返回 NaN
   */
  private chineseToNumber(text: string): number {
    const chineseNumbers: Record<string, number> = {
      '零': 0, '一': 1, '二': 2, '兩': 2, '三': 3, '四': 4, '五': 5,
      '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
    };
    
    // 直接匹配簡單的中文數字
    if (chineseNumbers[text] !== undefined) {
      return chineseNumbers[text];
    }
    
    // 處理"十"開頭的數字，如"十一"表示11
    if (text.startsWith('十')) {
      if (text.length === 1) return 10;
      const rest = text.substring(1);
      if (chineseNumbers[rest] !== undefined) {
        return 10 + chineseNumbers[rest];
      }
    }
    
    // 處理"一十"、"二十"等形式的數字
    if (text.length > 1 && text.endsWith('十')) {
      const prefix = text.substring(0, text.length - 1);
      if (chineseNumbers[prefix] !== undefined) {
        return chineseNumbers[prefix] * 10;
      }
    }
    
    // 處理完整的"二十三"等形式
    if (text.length > 2 && text.includes('十')) {
      const parts = text.split('十');
      if (parts.length === 2 && chineseNumbers[parts[0]] !== undefined && chineseNumbers[parts[1]] !== undefined) {
        return chineseNumbers[parts[0]] * 10 + chineseNumbers[parts[1]];
      }
    }
    
    return NaN;
  }

  /**
   * 從文本中提取最可能的數量信息
   * @param text 輸入文本
   * @param defaultQuantity 默認數量
   * @returns 數量值
   */
  extractQuantity(text: string, defaultQuantity = 1): number {
    // 匹配阿拉伯數字的數量表達方式
    const digitPattern = /(\d+)[\s]*(份|個|杯|份|客|盒|碗|片|瓶|罐|袋|盤|塊|杯子|包|條|支|根)/i;
    const digitMatch = text.match(digitPattern);
    
    if (digitMatch && digitMatch[1]) {
      const quantity = parseInt(digitMatch[1], 10);
      return isNaN(quantity) ? defaultQuantity : quantity;
    }
    
    // 匹配中文數字的數量表達方式
    const chinesePattern = /(一|二|兩|三|四|五|六|七|八|九|十|一十一|一十二|二十|二十一|二十二|二十三|二十四|二十五|三十)[\s]*(份|個|杯|份|客|盒|碗|片|瓶|罐|袋|盤|塊|杯子|包|條|支|根)/i;
    const chineseMatch = text.match(chinesePattern);
    
    if (chineseMatch && chineseMatch[1]) {
      const chineseNum = chineseMatch[1];
      const quantity = this.chineseToNumber(chineseNum);
      return isNaN(quantity) ? defaultQuantity : quantity;
    }
    
    return defaultQuantity;
  }

  /**
   * 從一段文本中找到最接近的菜單項名稱匹配
   * @param text 輸入文本
   * @param names 可能的菜單項名稱列表
   * @param threshold 最低相似度閾值
   * @returns 最佳匹配名稱和相似度
   */
  findBestMatch(text: string, names: string[], threshold = 0.6): { match: string | null, similarity: number } {
    let bestMatch = null;
    let bestSimilarity = 0;

    for (const name of names) {
      // 直接包含檢查
      if (text.includes(name)) {
        return { match: name, similarity: 1.0 };
      }

      // 計算所有可能的子字符串的相似度
      const words = text.split(/\s+/);
      for (let i = 0; i < words.length; i++) {
        for (let j = i; j < words.length; j++) {
          const phrase = words.slice(i, j + 1).join(' ');
          const similarity = this.stringSimilarity(phrase, name);
          
          if (similarity > bestSimilarity) {
            bestSimilarity = similarity;
            bestMatch = name;
          }
        }
      }
    }

    return bestSimilarity >= threshold 
      ? { match: bestMatch, similarity: bestSimilarity }
      : { match: null, similarity: 0 };
  }
}
