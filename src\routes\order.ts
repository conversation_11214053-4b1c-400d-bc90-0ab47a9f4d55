import { Router } from 'express';
import OrderService from '../services/OrderService.js';
import MockOrderService from '../services/MockOrderService.js';
import { RequestHandler } from '../types/express-route-types.js';

// 由於 Firebase 權限問題，使用模擬服務
// 當 Firebase 權限問題解決後，可以切回 OrderService
const activeOrderService = MockOrderService;

const router = Router();

// 創建訂單
router.post('/', (async (req, res) => {
  try {
    const { items, userId } = req.body;
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: '訂單必須包含至少一項商品' });
    }
    
    // 記錄收到的請求數據
    console.log('接收到訂單創建請求:', { 
      items: items.length + ' 項商品',
      hasUserId: !!userId,
      itemDetails: items.map(item => ({ 
        name: item.name, 
        quantity: item.quantity 
      }))
    });
      // 確保所有項目都有名稱、數量和價格
    const validItems = items.filter(item => 
      item && item.name && 
      typeof item.quantity === 'number' && item.quantity > 0 &&
      typeof item.price === 'number' && item.price >= 0
    );
    
    if (validItems.length === 0) {
      return res.status(400).json({ error: '訂單中沒有有效的商品項目' });
    }
      // 嚴格驗證 userId，只有當它是非空有效字串時才傳遞
    let validUserId = null;
    if (userId && typeof userId === 'string' && userId.trim() !== '') {
      validUserId = userId.trim();  // 確保移除前後空格
      console.log('有效的 userId:', validUserId);
    } else {
      console.log('無效的 userId，將使用 null:', userId);
    }
      // 使用驗證後的 userId 建立訂單，使用模擬服務
    const order = await activeOrderService.createOrder(validItems, validUserId);
    
    console.log('訂單創建成功:', {
      id: order.id,
      itemCount: order.items.length,
      totalAmount: order.totalAmount
    });
    
    return res.status(201).json(order);
  } catch (error) {
    console.error('創建訂單失敗:', error);
    // 提供更詳細的錯誤信息
    const errorMessage = error instanceof Error ? error.message : '未知錯誤';
    return res.status(500).json({ 
      error: '創建訂單時發生錯誤', 
      details: errorMessage 
    });
  }
}) as RequestHandler);

// 確認訂單
router.put('/:id/confirm', (async (req, res) => {  try {
    const { id } = req.params;
    const order = await activeOrderService.confirmOrder(id);
    return res.status(200).json(order);
  } catch (error) {
    console.error('確認訂單失敗:', error);
    return res.status(500).json({ error: '確認訂單時發生錯誤' });
  }
}) as RequestHandler);

// 獲取訂單資訊
router.get('/:id', (async (req, res) => {  try {
    const { id } = req.params;
    const order = await activeOrderService.getOrder(id);
    
    if (!order) {
      return res.status(404).json({ error: '找不到該訂單' });
    }
    
    return res.status(200).json(order);
  } catch (error) {
    console.error('獲取訂單資訊失敗:', error);
    return res.status(500).json({ error: '獲取訂單資訊時發生錯誤' });
  }
}) as RequestHandler);

// 更新訂單狀態
router.put('/:id/status', (async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (!['pending', 'confirmed', 'preparing', 'delivered'].includes(status)) {
      return res.status(400).json({ error: '無效的訂單狀態' });
    }
      await activeOrderService.updateOrderStatus(id, status);
    return res.status(204).send();
  } catch (error) {
    console.error('更新訂單狀態失敗:', error);
    return res.status(500).json({ error: '更新訂單狀態時發生錯誤' });
  }
}) as RequestHandler);

export default router;
