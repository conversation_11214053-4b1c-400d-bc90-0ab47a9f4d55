Feature: Natural language ordering As a customer I want to order food using natural language (text or voice) So that I can place my order quickly and intuitively

Scenario: Customers use text input to place orders
  Given a customer is on the ordering page
  When the customer enters "I would like to order a Big Mac and a cup of corn soup" into the natural language input field
  Then the system should identify "Big Mac Extra Value Meal" and "corn soup"
  And query the database/RAG for details (name, price, image, availability) of "Big Mac Extra Value Meal" and "corn soup"
  And present the identified items and their details back to the customer using natural language for confirmation (e.g., "Would you like to order a Big Mac Extra Value Meal and a side of corn soup?")

Scenario: Customers order food using voice input
  Given a customer is on the ordering page
  When the customer uses voice input saying "I want a McCrispy Chicken (2 pieces) Extra Value Meal"
  Then the system should transcribe the voice input to text
  And identify "McCrispy Chicken (2 pieces) Extra Value Meal"
  And query the database/RAG for details (name, price, image, availability) of "McCrispy Chicken (2 pieces) Extra Value Meal"
  And present the identified item and its details back to the customer using natural language for confirmation (e.g., "You chose a McCrispy Chicken (2 pieces) Extra Value Meal, right?")

Scenario: Customer modifies order content
  Given the system has presented the identified items for confirmation
  When the customer says "Corn soup replaced with Coke"
  Then the system should remove "Corn soup" from the proposed order
  And query the database/RAG for details of "Coke"
  And present the modified order (Big Mac Extra Value Meal, Coke) back to the customer using natural language for confirmation

Scenario: The system cannot recognize the meal
  Given a customer is on the ordering page
  When the customer enters "Give me a mysterious magic burger"
  Then the system should indicate that the item "mysterious magic burger" was not found
  And suggest checking the menu or rephrasing the request

Scenario: Customer modifies order - adds items
  Given the system has presented the identified items for confirmation (Big Mac Extra Value Meal)
  When the customer says "Add one fries"
  Then the system should identify "fries"
  And query the database/RAG for details of "fries"
  And add "fries" to the proposed order
  And present the updated order (Big Mac Extra Value Meal, fries) back to the customer using natural language for confirmation (e.g., "OK, I've added one fries for you. Now it includes the Big Mac Extra Value Meal and one fries. Are you sure?")

Scenario: Customer modifies order - removes item
  Given the system has presented the identified items for confirmation (Big Mac Extra Value Meal, Corn soup)
  When the customer says "No more corn soup"
  Then the system should remove "Corn soup" from the proposed order
  And present the updated order (Big Mac Extra Value Meal) back to the customer using natural language for confirmation (e.g., "Ok, the corn soup has been removed. Currently there is only the Big Mac Extra Value Meal. Are you sure?")

Scenario: Some meals are not recognized by the system
  Given a customer is on the ordering page
  When the customer enters "I want a cheeseburger and some kind of pie"
  Then the system should identify "cheeseburger"
  And indicate that the item "some kind of pie" was not found
  And suggest checking the menu or rephrasing the request for the unknown item

Scenario: Customers asking for meal information
  Given a customer is on the ordering page
  When the customer enters "What is Big Mac?"
  Then the system should query the database/RAG for details of "Big Mac" (description, ingredients, etc.)
  And present the information about "Big Mac" to the customer using natural language (e.g., "The Big Mac is a classic double beef burger that includes...")

Scenario: Customer confirms order
  Given the system has presented the final order for confirmation (Big Mac Extra Value Meal, Coke)
  When the customer says "confirm" 或 "no problem"
  Then the system should process the order
  And display an order confirmation message (e.g., "Your order has been sent！")

Scenario: Customer cancels order
  Given the system has presented the identified items for confirmation (Big Mac Extra Value Meal, Corn soup)
  When the customer says "Cancel" 或 "I don't want it anymore"
  Then the system should cancel the current order
  And display a message indicating the cancellation (e.g., "OK, your order has been cancelled.")