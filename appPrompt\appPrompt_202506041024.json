{"prompt": "You are a natural language ordering system for a fast food restaurant.  Take the customer's order using natural language (text or voice).  Identify the items they request, confirm their order, and allow them to modify or cancel it. Use the provided menu to validate items and prices. Respond in a conversational and helpful manner.", "parameters": {"feature": "Natural language ordering", "scenario": "Customer cancels order", "given": ["a customer is on the ordering page", "the system has presented the identified items for confirmation", "the system has presented the identified items for confirmation (Big Mac Extra Value Meal)", "the system has presented the identified items for confirmation (Big Mac Extra Value Meal, Corn soup)", "the system has presented the final order for confirmation (Big Mac Extra Value Meal, Coke)"], "when": ["the customer enters \"I would like to order a Big Mac and a cup of corn soup\" into the natural language input field", "the customer uses voice input saying \"I want a McCrispy Chicken (2 pieces) Extra Value Meal\"", "the customer says \"Corn soup replaced with Coke\"", "the customer enters \"Give me a mysterious magic burger\"", "the customer says \"Add one fries\"", "the customer says \"No more corn soup\"", "the customer enters \"I want a cheeseburger and some kind of pie\"", "the customer enters \"What is Big Mac?\"", "the customer says \"confirm\" or \"no problem\"", "the customer says \"Cancel\" or \"I don't want it anymore\""], "then": ["the system should identify \"Big Mac Extra Value Meal\" and \"corn soup\"", "query the database/RAG for details (name, price, image, availability) of \"Big Mac Extra Value Meal\" and \"corn soup\"", "present the identified items and their details back to the customer using natural language for confirmation (e.g., \"Would you like to order a Big Mac Extra Value Meal and a side of corn soup?\")", "the system should transcribe the voice input to text", "identify \"McCrispy Chicken (2 pieces) Extra Value Meal\"", "...", "the system should cancel the current order", "display a message indicating the cancellation (e.g., \"OK, your order has been cancelled.\")"], "menu": [{"category": "Extra Value Meals", "items": [{"id": "1", "name_en": "Big Mac Extra Value Meal", "price": 143}, {"id": "11", "name_en": "Quarter Pounder with Cheese Extra Value Meal", "price": 157}, {"id": "13", "name_en": "BLT Angus Beef Burger Extra Value Meal", "price": 187}, {"id": "15", "name_en": "Mushroom Angus Beef Burger Extra Value Meal", "price": 197}, {"id": "18", "name_en": "Parmesan Chef Chicken Burger Extra Value Meal", "price": 192}, {"id": "17", "name_en": "Parmesan Angus Beef Burger Extra Value Meal", "price": 192}, {"id": "3", "name_en": "Grilled Chicken Burger Extra Value Meal", "price": 148}, {"id": "14", "name_en": "BLT Grilled Chicken Burger Extra Value Meal", "price": 187}, {"id": "10", "name_en": "Filet-O-Fish Extra Value Meal", "price": 117}, {"id": "4", "name_en": "McChicken Extra Value Meal", "price": 113}, {"id": "7", "name_en": "McChicken Burger Extra Value Meal", "price": 143}, {"id": "12", "name_en": "Double Quarter Pounder with Cheese Extra Value Meal", "price": 197}, {"id": "2", "name_en": "Double Cheeseburger Extra Value Meal", "price": 137}, {"id": "9", "name_en": "Double McChicken Extra Value Meal", "price": 143}]}, {"category": "Fried Chicken", "items": [{"id": "6", "name_en": "McNuggets (10 pieces) Extra Value Meal", "price": 174}, {"id": "5", "name_en": "McNuggets (6 pieces) Extra Value Meal", "price": 133}, {"id": "8", "name_en": "McCrispy Chicken (2 pieces) Extra Value Meal", "price": 191}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-04T10:24:43.620Z", "aiGenerated": true}}