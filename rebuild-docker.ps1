# 快速重新構建 Docker 映像腳本
# 適用於開發階段快速重新部署

Write-Host "=== 自然語言點餐系統 - 快速重新構建 ===" -ForegroundColor Green

# 檢查 Docker 是否運行
try {
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker not running"
    }
} catch {
    Write-Host "錯誤: Docker 未運行，請啟動 Docker Desktop" -ForegroundColor Red
    exit 1
}

Write-Host "1. 停止現有容器..." -ForegroundColor Blue
docker-compose down

Write-Host "2. 清理舊映像..." -ForegroundColor Blue
docker rmi natural-order_natural-order 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "舊映像已清理" -ForegroundColor Green
} else {
    Write-Host "舊映像不存在，跳過清理" -ForegroundColor Yellow
}

Write-Host "3. 重新構建映像..." -ForegroundColor Blue
docker-compose build --no-cache

Write-Host "4. 啟動服務..." -ForegroundColor Blue
docker-compose up -d

Write-Host "5. 檢查服務狀態..." -ForegroundColor Blue
Start-Sleep -Seconds 5
docker-compose ps

Write-Host ""
Write-Host "✅ 重新構建完成！" -ForegroundColor Green
Write-Host "🌐 訪問地址: http://localhost:3005" -ForegroundColor Cyan
Write-Host "📋 會話測試頁面: http://localhost:3005/session-test.html" -ForegroundColor Cyan
Write-Host ""
Write-Host "查看日誌: docker-compose logs -f" -ForegroundColor Yellow
Write-Host "停止服務: docker-compose down" -ForegroundColor Yellow
