import { Router, Request, Response } from 'express';
import { PromptEngine } from '../services/PromptEngine.js';

const router = Router();
const promptEngine = new PromptEngine();

// 定義處理器函數
/**
 * 驗證 BDD 語法
 */
const validateBDDHandler = (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      return res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
    }
    
    const validation = promptEngine.validateBDDSyntax(bddText);
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    console.error('BDD 驗證錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 驗證 AAprompt 語法
 */
const validateAAPromptHandler = (req: Request, res: Response) => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      return res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
    }
    
    const validation = promptEngine.validateAAPromptSyntax(aaText);
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    console.error('AAprompt 驗證錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 BDD 生成 APPprompt
 */
const generateFromBDDHandler = (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      return res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
    }
    
    // 先驗證 BDD 語法
    const validation = promptEngine.validateBDDSyntax(bddText);
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: '無效的 BDD 語法',
        details: validation.errors
      });
    }
    
    if (!validation.spec) {
      return res.status(400).json({
        success: false,
        error: '無法生成有效的 BDD 規範'
      });
    }
    
    // 生成 APPprompt
    const appPrompt = promptEngine.generateFromBDD(validation.spec);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 AAprompt 生成 APPprompt
 */
const generateFromAAPromptHandler = (req: Request, res: Response) => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      return res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
    }
    
    // 先驗證 AAprompt 語法
    const validation = promptEngine.validateAAPromptSyntax(aaText);
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: '無效的 AAprompt 語法',
        details: validation.errors
      });
    }
    
    if (!validation.prompt) {
      return res.status(400).json({
        success: false,
        error: '無法生成有效的 AAprompt'
      });
    }
    
    // 生成 APPprompt
    const appPrompt = promptEngine.generateFromAA(validation.prompt);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從自然語言生成 APPprompt
 */
const generateFromNaturalHandler = (req: Request, res: Response) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        error: '請提供自然語言文本'
      });
    }
    
    // 生成 APPprompt
    const appPrompt = promptEngine.generateFromNaturalLanguage(text);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

// 註冊路由
// 使用 Express 應用程式編程介面的正確型式
router.post('/validate-bdd', function(req, res) {
  validateBDDHandler(req, res);
});
router.post('/validate-aaprompt', function(req, res) {
  validateAAPromptHandler(req, res);
});
router.post('/generate-from-bdd', function(req, res) {
  generateFromBDDHandler(req, res);
});
router.post('/generate-from-aaprompt', function(req, res) {
  generateFromAAPromptHandler(req, res);
});
router.post('/generate-from-natural', function(req, res) {
  generateFromNaturalHandler(req, res);
});

export default router;
