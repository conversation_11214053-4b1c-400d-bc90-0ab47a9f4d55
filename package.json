{"name": "natural-order", "version": "1.0.0", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "cross-env NODE_ENV=development PORT=3004 tsx src/index.ts", "dev:prod-port": "cross-env NODE_ENV=production PORT=3005 tsx src/index.ts", "test": "tsx src/test.ts", "test:watch": "nodemon --exec tsx src/test.ts"}, "keywords": ["natural language", "ordering system", "menu management"], "author": "", "license": "ISC", "description": "自然語言點餐系統 - 支援 BDD/AAprompt 輸入和菜單管理", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/multer": "^1.4.12", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase": "^11.8.1", "form-data": "^4.0.2", "multer": "^2.0.0", "node-fetch": "^2.7.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.0.3", "tsx": "^4.19.4"}}