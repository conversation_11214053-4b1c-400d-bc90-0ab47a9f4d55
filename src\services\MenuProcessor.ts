import fs from 'node:fs';
import path from 'node:path';
import { parse } from 'csv-parse/sync';
import { MenuItem, MenuData, MenuUploadResult, MenuValidationRule, MenuProcessingOptions } from '../types/menu.js';

export class MenuProcessor {
  private validationRules: MenuValidationRule[] = [
    { field: 'id', required: true, type: 'string', minLength: 1 },
    { field: 'name_zh', required: true, type: 'string', minLength: 1 },
    { field: 'price', required: true, type: 'number', min: 0 }
  ];
  /**
   * 處理上傳的 CSV 菜單檔案
   */  async processMenuFile(
    filePath: string, 
    restaurantId: string,
    options: MenuProcessingOptions = {}
  ): Promise<MenuUploadResult> {
    try {
      // 讀取檔案
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      
      // 解析 CSV
      const rawData = parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true
      });

      // 預處理數據，支援不同欄位名稱格式
      const processedData = this.preprocessData(rawData);

      // 驗證數據
      const validationResult = this.validateMenuData(processedData, options);
      if (!validationResult.success) {
        return validationResult;
      }

      // 轉換為標準格式
      const menuItems = this.convertToMenuItems(processedData, options);
      
      // 自動分類
      const categories = this.categorizeItems(menuItems, options);
      
      // 構建完整菜單數據
      const menuData: MenuData = {
        restaurant_id: restaurantId,
        restaurant_name: this.extractRestaurantName(filePath),
        categories: categories,
        last_updated: new Date(),
        version: this.generateVersion()
      };

      return {
        success: true,
        data: menuData,
        warnings: validationResult.warnings
      };    } catch (error) {
      return {
        success: false,
        errors: [`處理檔案時發生錯誤: ${error instanceof Error ? error.message : '未知錯誤'}`]
      };
    }
  }

  /**
   * 驗證菜單數據
   */
  private validateMenuData(data: any[], options: MenuProcessingOptions): MenuUploadResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (options.skipValidation) {
      return { success: true, warnings };
    }

    // 檢查必填欄位
    data.forEach((item, index) => {
      this.validationRules.forEach(rule => {
        const value = item[rule.field];
        
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors.push(`第 ${index + 2} 行: 缺少必填欄位 '${rule.field}'`);
        }

        if (value !== undefined && value !== null && value !== '') {
          // 類型檢查
          if (rule.type === 'number' && isNaN(Number(value))) {
            errors.push(`第 ${index + 2} 行: '${rule.field}' 應該是數字`);
          }

          // 範圍檢查
          if (rule.type === 'number' && rule.min !== undefined && Number(value) < rule.min) {
            errors.push(`第 ${index + 2} 行: '${rule.field}' 不能小於 ${rule.min}`);
          }

          if (rule.type === 'string' && rule.minLength && value.length < rule.minLength) {
            errors.push(`第 ${index + 2} 行: '${rule.field}' 長度不能小於 ${rule.minLength}`);
          }
        }
      });
    });

    // 檢查 ID 唯一性
    const ids = data.map(item => item.id).filter(id => id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      errors.push(`發現重複的 ID: ${duplicateIds.join(', ')}`);
    }

    // 檢查價格合理性
    data.forEach((item, index) => {
      if (item.price && Number(item.price) > 10000) {
        warnings.push(`第 ${index + 2} 行: 價格 ${item.price} 似乎過高，請確認`);
      }
    });

    return {
      success: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }

  /**
   * 轉換為標準菜單項目格式
   */
  private convertToMenuItems(data: any[], options: MenuProcessingOptions): MenuItem[] {
    return data.map(item => {
      const menuItem: MenuItem = {
        id: String(item.id),
        name_zh: item.name_zh,
        price: Number(item.price),
        availability: true
      };

      // 可選欄位
      if (item.name_en) menuItem.name_en = item.name_en;
      if (item.name_jp) menuItem.name_jp = item.name_jp;
      if (item.price_jp) menuItem.price_jp = Number(item.price_jp);
      if (item.category) menuItem.category = item.category;
      if (item.description) menuItem.description = item.description;
      if (item.image_url) menuItem.image_url = item.image_url;
      if (item.size_en) menuItem.size_en = item.size_en;
      if (item.size_zh) menuItem.size_zh = item.size_zh;
      if (item.size_jp) menuItem.size_jp = item.size_jp;
      if (item.main_item) menuItem.main_item = item.main_item;
      if (item.side_item) menuItem.side_item = item.side_item;
      if (item.drink_item) menuItem.drink_item = item.drink_item;

      // 自動偵測分類
      if (options.autoDetectCategory && !menuItem.category) {
        menuItem.category = this.detectCategory(menuItem);
      }

      return menuItem;
    });
  }
  /**
   * 自動偵測餐點分類
   */
  private detectCategory(item: MenuItem): string {
    // 如果項目已經有分類，直接返回
    if (item.category) {
      return item.category;
    }
    
    const name = item.name_zh.toLowerCase();
    
    // 炸雞類（KFC特有）
    if (name.includes('脆雞') || name.includes('炸雞') || name.includes('雞塊') || 
        name.includes('雞翅') || name.includes('上校') || name.includes('咔啦') || 
        name.includes('麥脆雞')) {
      return '炸雞類';
    }
    
    // 套餐
    if (name.includes('套餐') || name.includes('meal') || item.main_item) {
      return '套餐';
    }
    
    // 飲料
    if (name.includes('可樂') || name.includes('茶') || name.includes('咖啡') || 
        name.includes('果汁') || name.includes('飲料') || name.includes('奶昔') || 
        item.size_zh || item.size_en) {
      return '飲料';
    }
    
    // 漢堡
    if (name.includes('堡') || name.includes('burger') || name.includes('麥克') || 
        name.includes('三明治') || name.includes('sandwich')) {
      return '漢堡';
    }
    
    // 配菜
    if (name.includes('薯條') || name.includes('薯餅') || name.includes('沙拉') || 
        name.includes('玉米') || name.includes('點心') || name.includes('甜點') || 
        name.includes('fries') || name.includes('sides')) {
      return '配菜';
    }

    return '其他';
  }

  /**
   * 將餐點按分類組織
   */
  private categorizeItems(items: MenuItem[], options: MenuProcessingOptions) {
    const categoryMap = new Map<string, MenuItem[]>();

    items.forEach(item => {
      const category = item.category || '其他';
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(item);
    });

    return Array.from(categoryMap.entries()).map(([categoryName, items]) => ({
      id: this.generateCategoryId(categoryName),
      name_zh: categoryName,
      name_en: this.translateCategoryToEnglish(categoryName),
      name_jp: this.translateCategoryToJapanese(categoryName),
      items: items.sort((a, b) => a.name_zh.localeCompare(b.name_zh))
    }));
  }
  /**
   * 生成分類 ID
   */
  private generateCategoryId(categoryName: string): string {
    const categoryMap: { [key: string]: string } = {
      '漢堡': 'burgers',
      '飲料': 'drinks',
      '套餐': 'meals',
      '配菜': 'sides',
      '炸雞類': 'fried_chicken',
      '蛋塔': 'egg_tarts',
      '蛋撻': 'egg_tarts',
      '個人餐': 'individual_meals',
      '多人餐/炸雞桶': 'family_meals_buckets',
      '配餐': 'side_sets',
      '點心/小食': 'snacks_light_meals',
      '早餐菜單': 'breakfast_menu',
      '其他': 'others'
    };
    return categoryMap[categoryName] || 'others';
  }

  /**
   * 翻譯分類名稱為英文
   */
  private translateCategoryToEnglish(categoryName: string): string {
    const translations: { [key: string]: string } = {
      '漢堡': 'Burgers',
      '飲料': 'Drinks',
      '套餐': 'Extra Value Meals',
      '配菜': 'Sides',
      '炸雞類': 'Fried Chicken',
      '蛋塔': 'Egg Tarts',
      '蛋撻': 'Egg Tarts',
      '個人餐': 'Individual Meals',
      '多人餐/炸雞桶': 'Family Meals/Chicken Buckets',
      '配餐': 'Side Sets',
      '點心/小食': 'Snacks/Light Meals',
      '早餐菜單': 'Breakfast Menu',
      '其他': 'Others'
    };
    return translations[categoryName] || categoryName;
  }

  /**
   * 翻譯分類名稱為日文
   */
  private translateCategoryToJapanese(categoryName: string): string {
    const translations: { [key: string]: string } = {
      // 中文到日文
      '漢堡': 'バーガー',
      '飲料': 'ドリンク',
      '套餐': 'バリューセット',
      '配菜': 'サイドメニュー',
      '炸雞類': 'フライドチキン',
      '蛋塔': 'エッグタルト',
      '蛋撻': 'エッグタルト',
      '個人餐': '個人セット',
      '多人餐/炸雞桶': 'ファミリーセット/チキンバケツ',
      '配餐': 'サイドセット',
      '點心/小食': 'スナック/軽食',
      '早餐菜單': '朝食メニュー',
      '其他': 'その他',
      // 英文到日文
      'Burgers': 'バーガー',
      'Drinks': 'ドリンク',
      'Extra Value Meals': 'バリューセット',
      'Extra value meals': 'バリューセット',
      'Sides': 'サイドメニュー',
      'Fried Chicken': 'フライドチキン',
      'Fired Chicken': 'フライドチキン', // 處理拼寫錯誤
      'Egg Tarts': 'エッグタルト',
      'Individual Meals': '個人セット',
      'Personal Meals': '個人セット',
      'Family Meals/Chicken Buckets': 'ファミリーセット/チキンバケツ',
      'Side Sets': 'サイドセット',
      'Snacks/Light Meals': 'スナック/軽食',
      'Breakfast Menu': '朝食メニュー',
      'Others': 'その他'
    };
    return translations[categoryName] || categoryName;
  }

  /**
   * 從檔案路徑提取餐廳名稱
   */
  private extractRestaurantName(filePath: string): string {
    const fileName = path.basename(filePath, path.extname(filePath));
    
    // 如果檔案名包含 mcdonalds，返回McDonald's
    if (fileName.toLowerCase().includes('mcdonalds') || fileName.toLowerCase().includes('麥當勞')) {
      return "McDonald's";
    }
    
    // 如果檔案名包含 kfc，返回KFC
    if (fileName.toLowerCase().includes('kfc') || fileName.toLowerCase().includes('肯德基')) {
      return 'KFC';
    }
    
    return fileName;
  }

  /**
   * 生成版本號
   */
  private generateVersion(): string {
    return new Date().toISOString().split('T')[0].replace(/-/g, '.');
  }

  /**
   * 匯出菜單為 JSON 格式
   */
  exportToJSON(menuData: MenuData): string {
    return JSON.stringify(menuData, null, 2);
  }

  /**
   * 匯出菜單為 CSV 格式
   */
  exportToCSV(menuData: MenuData): string {
    const headers = ['id', 'name_zh', 'name_en', 'price', 'category', 'description'];
    const rows = [headers.join(',')];

    menuData.categories.forEach(category => {
      category.items.forEach(item => {
        const row = [
          item.id,
          `"${item.name_zh}"`,
          `"${item.name_en || ''}"`,
          item.price.toString(),
          `"${item.category || ''}"`,
          `"${item.description || ''}"`
        ];
        rows.push(row.join(','));
      });
    });

    return rows.join('\n');
  }
  /**
   * 預處理數據，支援不同欄位名稱格式
   * 將常見的欄位名稱映射到標準欄位名稱
   */
  private preprocessData(data: any[]): any[] {
    if (data.length === 0) return data;

    // 檢查是否需要映射欄位
    const firstItem = data[0];
    
    // 定義各種可能的欄位名稱映射
    const nameFieldAliases = ['餐點名稱', '品項', '商品名稱', '名稱', '餐點', '品名', 'item_name', 'name', 'product_name', 'item'];
    const categoryFieldAliases = ['分類', '類別', '品項分類', '種類', 'item_category', 'category_name', 'type'];
    const priceFieldAliases = ['價格', '單價', '售價', '金額', 'item_price', 'unit_price', 'cost'];
    const idFieldAliases = ['編號', '序號', '品項編號', '商品編號', 'item_id', 'product_id', 'number'];
    
    // 檢查是否有任何需要映射的欄位
    const needsMapping = nameFieldAliases.some(field => firstItem[field] !== undefined) ||
                        categoryFieldAliases.some(field => firstItem[field] !== undefined) ||
                        priceFieldAliases.some(field => firstItem[field] !== undefined) ||
                        idFieldAliases.some(field => firstItem[field] !== undefined);
    
    if (!needsMapping) return data;

    console.log('檢測到非標準欄位名稱，進行欄位映射...');

    // 進行欄位名稱映射
    return data.map(item => {
      const mappedItem: any = { ...item };
      
      // 映射名稱欄位
      for (const field of nameFieldAliases) {
        if (item[field] !== undefined && mappedItem.name_zh === undefined) {
          mappedItem.name_zh = item[field];
          console.log(`將 "${field}" 欄位映射為 "name_zh"`);
          break;
        }
      }
      
      // 映射分類欄位
      for (const field of categoryFieldAliases) {
        if (item[field] !== undefined && mappedItem.category === undefined) {
          mappedItem.category = item[field];
          console.log(`將 "${field}" 欄位映射為 "category"`);
          break;
        }
      }
      
      // 映射價格欄位
      for (const field of priceFieldAliases) {
        if (item[field] !== undefined && mappedItem.price === undefined) {
          mappedItem.price = Number(item[field]);
          console.log(`將 "${field}" 欄位映射為 "price"`);
          break;
        }
      }
      
      // 映射ID欄位
      for (const field of idFieldAliases) {
        if (item[field] !== undefined && mappedItem.id === undefined) {
          mappedItem.id = String(item[field]);
          console.log(`將 "${field}" 欄位映射為 "id"`);
          break;
        }
      }
      
      return mappedItem;
    });
  }
}
