/**
 * MockOrderService.ts
 * 提供模擬的訂單服務功能，在 Firebase 權限問題解決前使用
 */
import { Timestamp } from '../config/firebase.js';

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
}

interface Order {
  id?: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'delivered';
  timestamp: any;  // 使用 any 以避免 Timestamp 類型問題
  userId?: string;
}

// 用於存儲臨時訂單數據
const mockOrders: Record<string, Order> = {};
let lastOrderId = 0;

/**
 * 生成唯一ID
 */
function generateOrderId(): string {
  lastOrderId++;
  const timestamp = Date.now();
  return `mock-order-${timestamp}-${lastOrderId}`;
}

export class MockOrderService {
  async createOrder(items: OrderItem[], userId: string | null = null): Promise<Order> {
    try {
      // 記錄收到的原始訂單項目
      console.log('[模擬服務] 收到訂單項目:', JSON.stringify(items, null, 2));
      
      // 合併相同商品的訂單項目
      const mergedItems = this.mergeOrderItems(items);
      console.log('[模擬服務] 合併後的訂單項目:', JSON.stringify(mergedItems, null, 2));
      
      // 計算總金額
      const totalAmount = mergedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      // 創建訂單數據基本結構
      const orderData: Order = {
        items: mergedItems,
        totalAmount,
        status: 'pending',
        timestamp: new Date()  // 使用 JavaScript Date 物件代替 Firestore Timestamp
      };
      
      // 只有在 userId 有實際值且為字串類型時才添加到訂單數據中
      if (userId && typeof userId === 'string' && userId.trim() !== '') {
        orderData.userId = userId.trim();
        console.log('[模擬服務] 添加有效的 userId 到訂單:', userId);
      } else {
        console.log('[模擬服務] 未添加 userId 到訂單，值無效或為空:', userId);
      }
      
      // 生成訂單 ID 並儲存
      const orderId = generateOrderId();
      mockOrders[orderId] = orderData;
      
      console.log('[模擬服務] 訂單已成功創建，ID:', orderId);
      
      return { ...orderData, id: orderId };
    } catch (error) {
      console.error('[模擬服務] 處理訂單時出錯:', error);
      throw error;
    }
  }
  
  // 合併相同商品的訂單項目並過濾測試數據
  private mergeOrderItems(items: OrderItem[]): OrderItem[] {
    // 先過濾無效和測試數據
    const validItems = items.filter(item => 
      item && 
      item.name && 
      typeof item.name === 'string' &&
      // 排除純數字名稱（可能是測試數據）
      !/^\d+$/.test(item.name.trim()) &&
      typeof item.quantity === 'number' && item.quantity > 0 &&
      typeof item.price === 'number' && item.price >= 0
    );
    
    console.log('[模擬服務] 過濾後的有效項目數:', validItems.length, '原始項目數:', items.length);
    
    // 使用 Map 根據商品名稱合併相同的商品
    const itemMap = new Map<string, OrderItem>();
    
    for (const item of validItems) {
      // 清理項目名稱，移除多餘的換行和空格
      const cleanName = item.name.replace(/\n\s+/g, ' ').trim();
      
      if (itemMap.has(cleanName)) {
        // 如果已存在相同名稱的商品，增加數量
        const existingItem = itemMap.get(cleanName)!;
        existingItem.quantity += item.quantity;
      } else {
        // 否則添加新項目，使用清理後的名稱
        itemMap.set(cleanName, { ...item, name: cleanName });
      }
    }
    
    // 將 Map 轉換為數組並返回
    return Array.from(itemMap.values());
  }
  
  // 確認訂單
  async confirmOrder(orderId: string): Promise<Order> {
    if (!mockOrders[orderId]) {
      throw new Error(`[模擬服務] 訂單不存在: ${orderId}`);
    }
    
    // 更新訂單狀態
    mockOrders[orderId].status = 'confirmed';
    
    // 模擬1分鐘後開始準備訂單
    setTimeout(() => {
      if (mockOrders[orderId]) {
        mockOrders[orderId].status = 'preparing';
        console.log(`[模擬服務] 訂單 ${orderId} 已進入準備階段`);
      }
    }, 60000);
    
    return { ...mockOrders[orderId], id: orderId };
  }
  
  // 獲取訂單資訊
  async getOrder(orderId: string): Promise<Order | null> {
    const order = mockOrders[orderId];
    
    if (!order) {
      return null;
    }
    
    return { ...order, id: orderId };
  }
  
  // 更新訂單狀態
  async updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
    if (!mockOrders[orderId]) {
      throw new Error(`[模擬服務] 訂單不存在: ${orderId}`);
    }
    
    mockOrders[orderId].status = status;
  }
}

export default new MockOrderService();
