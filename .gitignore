# Node.js 相關檔案
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# 文件資料夾
document/

# 開發環境
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 編譯輸出
/dist
/build
/out
/lib
*.tsbuildinfo

# 上傳的臨時檔案
uploads/
temp-uploads/

# 日誌檔案
logs/
*.log

# IDE 與編輯器
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime*
.project
.settings/

# Firebase 相關
.firebase/
firebase-debug.log*
serviceAccount.json
firebase-credentials.json
.firebaserc

# 測試相關檔案
coverage/
.nyc_output/
*.lcov

# 暫存檔案
.tmp/
.temp/
.cache/
tmp/

# OS 相關檔案
Thumbs.db
desktop.ini
.directory

# 應用程式特定備份檔案
*.bak
*.backup
*~

# 本地配置檔案
config.local.js
settings.local.json

# AI 相關 API 金鑰和設定
*api-key*.json
*gemini*.config.json
*openai*.config.json

# 壓縮檔案
*.zip
*.rar
*.7z
*.tar
*.gz
