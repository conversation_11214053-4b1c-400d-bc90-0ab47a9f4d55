{"prompt": "你是一個自然語言點餐系統，根據顧客的語音或文字輸入，識別餐點、查詢菜單數據並引導顧客完成點餐。如果顧客輸入的餐點名稱不明確或菜單中沒有，你需要提供建議或要求顧客更正。", "parameters": {"feature": "自然語言點餐", "scenario": "顧客取消訂單", "given": ["顧客在點餐頁面", "系統已顯示確認餐點", "系統已顯示確認餐點 (大麥克套餐)", "系統已顯示確認餐點 (大麥克套餐, 玉米湯)", "系統已顯示最終訂單確認 (大麥克套餐, 可樂)"], "when": "顧客說 \"取消\" 或 \"我不要了\"", "then": "系統應取消目前的訂單並顯示取消訊息 (例如：\"好的，您的訂單已取消。\")", "menu": [{"category": "套餐", "items": [{"id": "1", "name_zh": "大麥克套餐", "price": 143}, {"id": "11", "name_zh": "四盎司牛肉堡套餐", "price": 157}, {"id": "13", "name_zh": "安格斯培根生菜番茄牛肉堡套餐", "price": 187}, {"id": "15", "name_zh": "安格斯蘑菇牛肉堡套餐", "price": 197}, {"id": "18", "name_zh": "帕瑪森主廚雞堡套餐", "price": 192}, {"id": "17", "name_zh": "帕瑪森安格斯牛肉堡套餐", "price": 192}, {"id": "3", "name_zh": "烤雞堡套餐", "price": 148}, {"id": "14", "name_zh": "培根生菜番茄烤雞堡套餐", "price": 187}, {"id": "10", "name_zh": "麥香魚套餐", "price": 117}, {"id": "4", "name_zh": "麥香雞套餐", "price": 113}, {"id": "7", "name_zh": "麥香雞漢堡套餐", "price": 143}, {"id": "12", "name_zh": "雙層四盎司牛肉堡套餐", "price": 197}, {"id": "2", "name_zh": "雙層吉士漢堡套餐", "price": 137}, {"id": "9", "name_zh": "雙層麥香雞套餐", "price": 143}, {"id": "16", "name_zh": "蘑菇主廚雞堡套餐", "price": 197}]}, {"category": "炸雞類", "items": [{"id": "6", "name_zh": "麥克雞塊(10塊)套餐", "price": 174}, {"id": "5", "name_zh": "麥克雞塊(6塊)套餐", "price": 133}, {"id": "8", "name_zh": "麥脆雞(2塊)套餐", "price": 191}]}, {"category": "單點", "items": [{"id": "c1", "name_zh": "可樂", "price": 35}, {"id": "c2", "name_zh": "玉米湯", "price": 45}, {"id": "s1", "name_zh": "薯條", "price": 55}, {"id": "b1", "name_zh": "吉士漢堡", "price": 65}]}], "current_order": [{"id": "1", "name_zh": "大麥克套餐", "price": 143}, {"id": "c1", "name_zh": "可樂", "price": 35}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-04T10:26:36.678Z", "aiGenerated": true}}