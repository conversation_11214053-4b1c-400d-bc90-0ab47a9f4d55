/**
 * 會話管理器 - 處理多用戶會話隔離
 * 支援 Docker 部署的純前端會話管理方案
 */

class SessionManager {
  constructor() {
    this.sessionId = this.getOrCreateSessionId();
    this.appPrompt = null;
    this.language = 'zh-TW';
    this.menuData = null;

    console.log(`[SessionManager] 初始化會話: ${this.sessionId}`);

    // 初始化時檢查語言匹配
    this.initializeLanguageCheck();
  }

  /**
   * 獲取或創建會話ID
   */
  getOrCreateSessionId() {
    // 首先檢查 sessionStorage
    let sessionId = sessionStorage.getItem('naturalOrder_sessionId');

    if (!sessionId) {
      // 生成新的會話ID
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      sessionStorage.setItem('naturalOrder_sessionId', sessionId);
      console.log(`[SessionManager] 創建新會話: ${sessionId}`);
    } else {
      console.log(`[SessionManager] 使用現有會話: ${sessionId}`);
    }

    return sessionId;
  }

  /**
   * 初始化時檢查語言匹配
   */
  initializeLanguageCheck() {
    // 延遲執行，確保其他組件已初始化
    setTimeout(() => {
      try {
        // 檢查是否在開發頁面
        const isDevPage = window.location.pathname.includes('/dev');

        // 獲取當前頁面語言設定
        const currentPageLanguage = this.getCurrentPageLanguage();
        const savedLanguage = this.getLanguage();

        console.log(`[SessionManager] 初始化語言檢查 - 頁面語言: ${currentPageLanguage}, 保存的語言: ${savedLanguage}, 開發頁面: ${isDevPage}`);

        // 如果頁面語言與保存的語言不同，更新語言設定
        if (currentPageLanguage !== savedLanguage) {
          console.log(`[SessionManager] 檢測到語言變化，更新語言設定`);

          // 在開發頁面中，提供更寬鬆的語言檢查
          if (isDevPage) {
            console.log(`[SessionManager] 開發頁面模式：只更新語言設定，不清除數據`);
            this.language = currentPageLanguage;
            sessionStorage.setItem(`naturalOrder_language_${this.sessionId}`, currentPageLanguage);
          } else {
            this.setLanguage(currentPageLanguage);
          }
        } else {
          // 即使語言相同，也檢查APPprompt是否匹配（但在開發頁面中跳過）
          if (!isDevPage) {
            this.checkAndClearLanguageMismatchedAppPrompt(currentPageLanguage);
          }
        }
      } catch (error) {
        console.error(`[SessionManager] 初始化語言檢查時出錯:`, error);
      }
    }, 1000);
  }

  /**
   * 獲取當前頁面語言設定
   */
  getCurrentPageLanguage() {
    // 嘗試從多個來源獲取當前語言
    if (typeof getCurrentLanguage === 'function') {
      return getCurrentLanguage();
    }

    // 備用方案：從localStorage獲取
    const preferredLanguage = localStorage.getItem('preferredLanguage');
    if (preferredLanguage) {
      return preferredLanguage;
    }

    // 最終備用方案
    return 'zh-TW';
  }

  /**
   * 設置 APPprompt
   */
  setAppPrompt(appPrompt) {
    this.appPrompt = appPrompt;
    
    // 保存到 sessionStorage
    try {
      const appPromptStr = typeof appPrompt === 'string' ? appPrompt : JSON.stringify(appPrompt);
      sessionStorage.setItem(`naturalOrder_appPrompt_${this.sessionId}`, appPromptStr);
      console.log(`[SessionManager] 已保存 APPprompt 到會話: ${this.sessionId}`);
    } catch (error) {
      console.error(`[SessionManager] 保存 APPprompt 失敗:`, error);
    }
  }

  /**
   * 獲取 APPprompt
   */
  getAppPrompt() {
    if (this.appPrompt) {
      return this.appPrompt;
    }

    // 從 sessionStorage 載入
    try {
      const appPromptStr = sessionStorage.getItem(`naturalOrder_appPrompt_${this.sessionId}`);
      if (appPromptStr) {
        this.appPrompt = appPromptStr;
        console.log(`[SessionManager] 從會話載入 APPprompt: ${this.sessionId}`);
        return this.appPrompt;
      }
    } catch (error) {
      console.error(`[SessionManager] 載入 APPprompt 失敗:`, error);
    }

    return null;
  }

  /**
   * 設置語言
   */
  setLanguage(language) {
    const oldLanguage = this.getLanguage();

    // 如果語言發生變化，檢查並清除不匹配的APPprompt
    if (oldLanguage !== language) {
      console.log(`[SessionManager] 語言從 ${oldLanguage} 變更為 ${language}`);
      this.checkAndClearLanguageMismatchedAppPrompt(language);
    }

    this.language = language;
    sessionStorage.setItem(`naturalOrder_language_${this.sessionId}`, language);
    console.log(`[SessionManager] 設置語言: ${language} (會話: ${this.sessionId})`);
  }

  /**
   * 獲取語言
   */
  getLanguage() {
    const savedLanguage = sessionStorage.getItem(`naturalOrder_language_${this.sessionId}`);
    return savedLanguage || this.language;
  }

  /**
   * 檢查並清除語言不匹配的APPprompt和菜單數據
   */
  checkAndClearLanguageMismatchedAppPrompt(newLanguage) {
    // 檢查APPprompt語言匹配
    const currentAppPrompt = this.getAppPrompt();
    if (currentAppPrompt) {
      try {
        const appPromptData = JSON.parse(currentAppPrompt);
        const promptText = appPromptData.prompt || '';

        // 檢測APPprompt的語言
        let appPromptLanguage = 'zh-TW'; // 預設中文

        if (promptText.includes('日本語') ||
            promptText.includes('ファーストフード') ||
            promptText.includes('注文受付システム') ||
            promptText.includes('あなたは自然言語で注文を受け付ける') ||
            promptText.includes('顧客の注文を理解し')) {
          appPromptLanguage = 'ja-JP';
        } else if (promptText.includes('English') ||
                   promptText.includes('natural language ordering') ||
                   promptText.includes('fast food restaurant') ||
                   promptText.includes('order processing system')) {
          appPromptLanguage = 'en-US';
        }

        // 檢查語言是否匹配
        if (appPromptLanguage !== newLanguage) {
          console.log(`[SessionManager] APPprompt語言不匹配 (APPprompt: ${appPromptLanguage}, 新語言: ${newLanguage})，清除舊APPprompt`);
          this.appPrompt = null;
          sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
        } else {
          console.log(`[SessionManager] APPprompt語言匹配 (${appPromptLanguage})，保留現有APPprompt`);
        }

      } catch (error) {
        console.error(`[SessionManager] 檢查APPprompt語言時出錯:`, error);
        // 出錯時為了安全起見，清除APPprompt
        console.log(`[SessionManager] 由於解析錯誤，清除APPprompt以確保安全`);
        this.appPrompt = null;
        sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
      }
    }

    // 檢查菜單數據語言匹配
    const currentMenuData = this.getMenuData();
    if (currentMenuData) {
      try {
        // 檢測菜單數據的語言
        let menuLanguage = 'zh-TW'; // 預設中文

        // 檢查菜單項目的語言欄位
        if (currentMenuData.categories && currentMenuData.categories.length > 0) {
          const firstCategory = currentMenuData.categories[0];
          const firstItem = firstCategory.items && firstCategory.items[0];

          if (firstItem) {
            // 根據菜單項目的語言欄位判斷語言
            if (firstItem.name_jp || firstItem.price_jp) {
              menuLanguage = 'ja-JP';
            } else if (firstItem.name_en && !firstItem.name_zh) {
              menuLanguage = 'en-US';
            }
          }
        }

        // 檢查語言是否匹配
        if (menuLanguage !== newLanguage) {
          console.log(`[SessionManager] 菜單數據語言不匹配 (菜單: ${menuLanguage}, 新語言: ${newLanguage})，清除舊菜單數據`);
          this.menuData = null;
          sessionStorage.removeItem(`naturalOrder_menuData_${this.sessionId}`);

          // 菜單數據清除後，APPprompt也應該清除
          if (this.appPrompt) {
            console.log(`[SessionManager] 由於菜單數據清除，同時清除APPprompt`);
            this.appPrompt = null;
            sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
          }
        } else {
          console.log(`[SessionManager] 菜單數據語言匹配 (${menuLanguage})，保留現有菜單數據`);
        }

      } catch (error) {
        console.error(`[SessionManager] 檢查菜單數據語言時出錯:`, error);
        // 出錯時為了安全起見，清除菜單數據和APPprompt
        console.log(`[SessionManager] 由於解析錯誤，清除菜單數據和APPprompt以確保安全`);
        this.menuData = null;
        this.appPrompt = null;
        sessionStorage.removeItem(`naturalOrder_menuData_${this.sessionId}`);
        sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
      }
    }
  }

  /**
   * 設置菜單數據
   */
  setMenuData(menuData) {
    // 檢查是否有新的菜單數據
    const currentMenuData = this.getMenuData();
    const isNewMenu = !currentMenuData ||
                      JSON.stringify(currentMenuData) !== JSON.stringify(menuData);

    if (isNewMenu) {
      console.log(`[SessionManager] 檢測到新菜單數據，清除舊的 APPprompt`);
      // 清除舊的 APPprompt，因為菜單已更新
      this.appPrompt = null;
      sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
    }

    this.menuData = menuData;

    try {
      const menuDataStr = typeof menuData === 'string' ? menuData : JSON.stringify(menuData);
      sessionStorage.setItem(`naturalOrder_menuData_${this.sessionId}`, menuDataStr);
      console.log(`[SessionManager] 已保存菜單數據到會話: ${this.sessionId}`);
    } catch (error) {
      console.error(`[SessionManager] 保存菜單數據失敗:`, error);
    }
  }

  /**
   * 獲取菜單數據
   */
  getMenuData() {
    if (this.menuData) {
      return this.menuData;
    }

    try {
      const menuDataStr = sessionStorage.getItem(`naturalOrder_menuData_${this.sessionId}`);
      if (menuDataStr) {
        this.menuData = JSON.parse(menuDataStr);
        console.log(`[SessionManager] 從會話載入菜單數據: ${this.sessionId}`);
        return this.menuData;
      }
    } catch (error) {
      console.error(`[SessionManager] 載入菜單數據失敗:`, error);
    }

    return null;
  }

  /**
   * 處理點餐請求
   */
  async processOrder(customerInput) {
    const appPrompt = this.getAppPrompt();
    
    if (!appPrompt) {
      throw new Error('沒有可用的 APPprompt，請先上傳菜單並生成 APPprompt');
    }

    console.log(`[SessionManager] 處理點餐請求 (會話: ${this.sessionId}):`, customerInput);

    try {
      const response = await fetch('/api/prompt/process-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerRequest: customerInput,
          language: this.getLanguage(),
          sessionId: this.sessionId,
          appPrompt: appPrompt
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '處理訂單失敗');
      }

      console.log(`[SessionManager] 點餐請求處理成功 (會話: ${this.sessionId})`);
      return result.result;

    } catch (error) {
      console.error(`[SessionManager] 點餐請求處理失敗:`, error);
      throw error;
    }
  }

  /**
   * 生成 APPprompt
   */
  async generateAppPrompt(type, content, language) {
    console.log(`[SessionManager] 生成 APPprompt (會話: ${this.sessionId}):`, { type, language });

    try {
      const response = await fetch('/api/prompt/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: type,
          content: content,
          language: language || this.getLanguage(),
          sessionId: this.sessionId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '生成 APPprompt 失敗');
      }

      // 保存生成的 APPprompt
      this.setAppPrompt(JSON.stringify(result.appPrompt));
      this.setLanguage(language || this.getLanguage());

      console.log(`[SessionManager] APPprompt 生成成功 (會話: ${this.sessionId})`);
      return result.appPrompt;

    } catch (error) {
      console.error(`[SessionManager] APPprompt 生成失敗:`, error);
      throw error;
    }
  }

  /**
   * 清除會話數據
   */
  clearSession() {
    console.log(`[SessionManager] 清除會話數據: ${this.sessionId}`);
    
    // 清除 sessionStorage 中的會話數據
    sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
    sessionStorage.removeItem(`naturalOrder_language_${this.sessionId}`);
    sessionStorage.removeItem(`naturalOrder_menuData_${this.sessionId}`);
    sessionStorage.removeItem('naturalOrder_sessionId');
    
    // 重置內存中的數據
    this.appPrompt = null;
    this.language = 'zh-TW';
    this.menuData = null;
    
    // 生成新的會話ID
    this.sessionId = this.getOrCreateSessionId();
  }

  /**
   * 獲取會話狀態
   */
  getSessionStatus() {
    return {
      sessionId: this.sessionId,
      hasAppPrompt: !!this.getAppPrompt(),
      language: this.getLanguage(),
      hasMenuData: !!this.getMenuData()
    };
  }
}

// 創建全局會話管理器實例
window.sessionManager = new SessionManager();

// 導出給其他模組使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SessionManager;
}
