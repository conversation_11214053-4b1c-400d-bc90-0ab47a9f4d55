/**
 * 會話管理器 - 處理多用戶會話隔離
 * 支援 Docker 部署的純前端會話管理方案
 */

class SessionManager {
  constructor() {
    this.sessionId = this.getOrCreateSessionId();
    this.appPrompt = null;
    this.language = 'zh-TW';
    this.menuData = null;
    
    console.log(`[SessionManager] 初始化會話: ${this.sessionId}`);
  }

  /**
   * 獲取或創建會話ID
   */
  getOrCreateSessionId() {
    // 首先檢查 sessionStorage
    let sessionId = sessionStorage.getItem('naturalOrder_sessionId');
    
    if (!sessionId) {
      // 生成新的會話ID
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      sessionStorage.setItem('naturalOrder_sessionId', sessionId);
      console.log(`[SessionManager] 創建新會話: ${sessionId}`);
    } else {
      console.log(`[SessionManager] 使用現有會話: ${sessionId}`);
    }
    
    return sessionId;
  }

  /**
   * 設置 APPprompt
   */
  setAppPrompt(appPrompt) {
    this.appPrompt = appPrompt;
    
    // 保存到 sessionStorage
    try {
      const appPromptStr = typeof appPrompt === 'string' ? appPrompt : JSON.stringify(appPrompt);
      sessionStorage.setItem(`naturalOrder_appPrompt_${this.sessionId}`, appPromptStr);
      console.log(`[SessionManager] 已保存 APPprompt 到會話: ${this.sessionId}`);
    } catch (error) {
      console.error(`[SessionManager] 保存 APPprompt 失敗:`, error);
    }
  }

  /**
   * 獲取 APPprompt
   */
  getAppPrompt() {
    if (this.appPrompt) {
      return this.appPrompt;
    }

    // 從 sessionStorage 載入
    try {
      const appPromptStr = sessionStorage.getItem(`naturalOrder_appPrompt_${this.sessionId}`);
      if (appPromptStr) {
        this.appPrompt = appPromptStr;
        console.log(`[SessionManager] 從會話載入 APPprompt: ${this.sessionId}`);
        return this.appPrompt;
      }
    } catch (error) {
      console.error(`[SessionManager] 載入 APPprompt 失敗:`, error);
    }

    return null;
  }

  /**
   * 設置語言
   */
  setLanguage(language) {
    this.language = language;
    sessionStorage.setItem(`naturalOrder_language_${this.sessionId}`, language);
    console.log(`[SessionManager] 設置語言: ${language} (會話: ${this.sessionId})`);
  }

  /**
   * 獲取語言
   */
  getLanguage() {
    const savedLanguage = sessionStorage.getItem(`naturalOrder_language_${this.sessionId}`);
    return savedLanguage || this.language;
  }

  /**
   * 設置菜單數據
   */
  setMenuData(menuData) {
    this.menuData = menuData;
    
    try {
      const menuDataStr = typeof menuData === 'string' ? menuData : JSON.stringify(menuData);
      sessionStorage.setItem(`naturalOrder_menuData_${this.sessionId}`, menuDataStr);
      console.log(`[SessionManager] 已保存菜單數據到會話: ${this.sessionId}`);
    } catch (error) {
      console.error(`[SessionManager] 保存菜單數據失敗:`, error);
    }
  }

  /**
   * 獲取菜單數據
   */
  getMenuData() {
    if (this.menuData) {
      return this.menuData;
    }

    try {
      const menuDataStr = sessionStorage.getItem(`naturalOrder_menuData_${this.sessionId}`);
      if (menuDataStr) {
        this.menuData = JSON.parse(menuDataStr);
        console.log(`[SessionManager] 從會話載入菜單數據: ${this.sessionId}`);
        return this.menuData;
      }
    } catch (error) {
      console.error(`[SessionManager] 載入菜單數據失敗:`, error);
    }

    return null;
  }

  /**
   * 處理點餐請求
   */
  async processOrder(customerInput) {
    const appPrompt = this.getAppPrompt();
    
    if (!appPrompt) {
      throw new Error('沒有可用的 APPprompt，請先上傳菜單並生成 APPprompt');
    }

    console.log(`[SessionManager] 處理點餐請求 (會話: ${this.sessionId}):`, customerInput);

    try {
      const response = await fetch('/api/prompt/process-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerRequest: customerInput,
          language: this.getLanguage(),
          sessionId: this.sessionId,
          appPrompt: appPrompt
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '處理訂單失敗');
      }

      console.log(`[SessionManager] 點餐請求處理成功 (會話: ${this.sessionId})`);
      return result.result;

    } catch (error) {
      console.error(`[SessionManager] 點餐請求處理失敗:`, error);
      throw error;
    }
  }

  /**
   * 生成 APPprompt
   */
  async generateAppPrompt(type, content, language) {
    console.log(`[SessionManager] 生成 APPprompt (會話: ${this.sessionId}):`, { type, language });

    try {
      const response = await fetch('/api/prompt/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: type,
          content: content,
          language: language || this.getLanguage(),
          sessionId: this.sessionId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || '生成 APPprompt 失敗');
      }

      // 保存生成的 APPprompt
      this.setAppPrompt(JSON.stringify(result.appPrompt));
      this.setLanguage(language || this.getLanguage());

      console.log(`[SessionManager] APPprompt 生成成功 (會話: ${this.sessionId})`);
      return result.appPrompt;

    } catch (error) {
      console.error(`[SessionManager] APPprompt 生成失敗:`, error);
      throw error;
    }
  }

  /**
   * 清除會話數據
   */
  clearSession() {
    console.log(`[SessionManager] 清除會話數據: ${this.sessionId}`);
    
    // 清除 sessionStorage 中的會話數據
    sessionStorage.removeItem(`naturalOrder_appPrompt_${this.sessionId}`);
    sessionStorage.removeItem(`naturalOrder_language_${this.sessionId}`);
    sessionStorage.removeItem(`naturalOrder_menuData_${this.sessionId}`);
    sessionStorage.removeItem('naturalOrder_sessionId');
    
    // 重置內存中的數據
    this.appPrompt = null;
    this.language = 'zh-TW';
    this.menuData = null;
    
    // 生成新的會話ID
    this.sessionId = this.getOrCreateSessionId();
  }

  /**
   * 獲取會話狀態
   */
  getSessionStatus() {
    return {
      sessionId: this.sessionId,
      hasAppPrompt: !!this.getAppPrompt(),
      language: this.getLanguage(),
      hasMenuData: !!this.getMenuData()
    };
  }
}

// 創建全局會話管理器實例
window.sessionManager = new SessionManager();

// 導出給其他模組使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SessionManager;
}
