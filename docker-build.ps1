# Docker 構建和部署腳本
# 適用於 Windows PowerShell

Write-Host "=== 自然語言點餐系統 Docker 部署腳本 ===" -ForegroundColor Green

# 檢查 Docker 是否安裝
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 Docker，請先安裝 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 檢查 Docker Compose 是否可用
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 Docker Compose，請確保 Docker Desktop 已正確安裝" -ForegroundColor Red
    exit 1
}

# 檢查 .env 文件是否存在
if (-not (Test-Path ".env")) {
    Write-Host "警告: 未找到 .env 文件，正在複製 .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "已創建 .env 文件，請編輯此文件並填入您的 Firebase 配置" -ForegroundColor Yellow
        Write-Host "編輯完成後請重新運行此腳本" -ForegroundColor Yellow
        exit 0
    } else {
        Write-Host "錯誤: 未找到 .env.example 文件" -ForegroundColor Red
        exit 1
    }
}

# 創建必要的目錄
Write-Host "創建數據目錄..." -ForegroundColor Blue
if (-not (Test-Path "uploads")) {
    New-Item -ItemType Directory -Path "uploads" -Force | Out-Null
    Write-Host "已創建 uploads 目錄" -ForegroundColor Green
}

if (-not (Test-Path "appPrompt")) {
    New-Item -ItemType Directory -Path "appPrompt" -Force | Out-Null
    Write-Host "已創建 appPrompt 目錄" -ForegroundColor Green
}

# 顯示選項菜單
Write-Host "`n請選擇操作:" -ForegroundColor Cyan
Write-Host "1. 構建並啟動服務"
Write-Host "2. 快速重新構建 (清理舊映像後重新構建)"
Write-Host "3. 啟動服務 (docker-compose up -d)"
Write-Host "4. 停止服務 (docker-compose down)"
Write-Host "5. 查看服務狀態 (docker-compose ps)"
Write-Host "6. 查看日誌 (docker-compose logs -f)"
Write-Host "7. 重啟服務 (docker-compose restart)"
Write-Host "8. 清理所有容器和映像 (docker-compose down --rmi all)"
Write-Host "9. 退出"

$choice = Read-Host "`n請輸入選項 (1-9)"

switch ($choice) {
    "1" {
        Write-Host "正在構建並啟動服務..." -ForegroundColor Blue
        docker-compose up -d --build
        if ($LASTEXITCODE -eq 0) {
            Write-Host "服務已成功啟動!" -ForegroundColor Green
            Write-Host "訪問地址: http://localhost:3005" -ForegroundColor Cyan
        } else {
            Write-Host "服務啟動失敗，請檢查日誌" -ForegroundColor Red
        }
    }
    "2" {
        Write-Host "=== 快速重新構建模式 ===" -ForegroundColor Green
        Write-Host "1. 停止現有容器..." -ForegroundColor Blue
        docker-compose down

        Write-Host "2. 清理舊映像..." -ForegroundColor Blue
        docker rmi natural-order_natural-order 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "舊映像已清理" -ForegroundColor Green
        } else {
            Write-Host "舊映像不存在，跳過清理" -ForegroundColor Yellow
        }

        Write-Host "3. 重新構建映像..." -ForegroundColor Blue
        docker-compose build --no-cache

        Write-Host "4. 啟動服務..." -ForegroundColor Blue
        docker-compose up -d

        Write-Host "5. 檢查服務狀態..." -ForegroundColor Blue
        Start-Sleep -Seconds 5
        docker-compose ps

        Write-Host ""
        Write-Host "✅ 重新構建完成！" -ForegroundColor Green
        Write-Host "🌐 訪問地址: http://localhost:3005" -ForegroundColor Cyan
        Write-Host "📋 會話測試頁面: http://localhost:3005/session-test.html" -ForegroundColor Cyan
    }
    "3" {
        Write-Host "正在啟動服務..." -ForegroundColor Blue
        docker-compose up -d
        if ($LASTEXITCODE -eq 0) {
            Write-Host "服務已成功啟動!" -ForegroundColor Green
            Write-Host "訪問地址: http://localhost:3005" -ForegroundColor Cyan
        } else {
            Write-Host "服務啟動失敗，請檢查日誌" -ForegroundColor Red
        }
    }
    "4" {
        Write-Host "正在停止服務..." -ForegroundColor Blue
        docker-compose down
        Write-Host "服務已停止" -ForegroundColor Green
    }
    "5" {
        Write-Host "服務狀態:" -ForegroundColor Blue
        docker-compose ps
    }
    "6" {
        Write-Host "顯示日誌 (按 Ctrl+C 退出):" -ForegroundColor Blue
        docker-compose logs -f
    }
    "7" {
        Write-Host "正在重啟服務..." -ForegroundColor Blue
        docker-compose restart
        Write-Host "服務已重啟" -ForegroundColor Green
    }
    "8" {
        $confirm = Read-Host "確定要清理所有容器和映像嗎? (y/N)"
        if ($confirm -eq "y" -or $confirm -eq "Y") {
            Write-Host "正在清理..." -ForegroundColor Blue
            docker-compose down --rmi all
            Write-Host "清理完成" -ForegroundColor Green
        } else {
            Write-Host "已取消清理操作" -ForegroundColor Yellow
        }
    }
    "9" {
        Write-Host "退出腳本" -ForegroundColor Green
        exit 0
    }
    default {
        Write-Host "無效選項，請重新運行腳本" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n腳本執行完成" -ForegroundColor Green