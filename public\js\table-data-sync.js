/**
 * 表格數據同步腳本
 * 確保表格中的顯示數據與data-*屬性保持一致
 */

// 將同步函數暴露為全局函數，以便其他腳本調用
window.syncTableDataAttributes = function(forceUpdate = false) {
    const tables = document.querySelectorAll('.preview-table');
    if (tables.length === 0) {
        console.log('未找到訂單表格');
        return false;
    }
    
    console.log('開始同步表格數據與屬性 - ' + new Date().toLocaleTimeString());
    
    // 強制更新模式會確保每個項目的屬性都被更新
    const updateMode = forceUpdate ? '強制更新模式' : '一般同步模式';
    console.log(`同步模式: ${updateMode}`);
    
    tables.forEach(table => {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            // 1. 從可見文本到data-*屬性的同步
            const nameCell = row.querySelector('.item-name');
            const priceCell = row.querySelector('.item-price');
            const qtyElement = row.querySelector('.item-quantity');
            const totalCell = row.querySelector('.item-total');
            
            // 同步名稱
            if (nameCell && nameCell.textContent.trim() !== '' && nameCell.textContent.trim() !== '未知餐點') {
                const visibleName = nameCell.textContent.trim();
                const dataName = row.getAttribute('data-name');
                
                if (!dataName || dataName === '未知餐點' || dataName !== visibleName) {
                    row.setAttribute('data-name', visibleName);
                    console.log(`已將行的data-name從"${dataName}"更新為"${visibleName}"`);
                }
            }
            
            // 同步價格
            if (priceCell) {
                const visiblePrice = parseFloat(priceCell.textContent.replace('NT$', '').trim());
                const dataPrice = parseFloat(row.getAttribute('data-price') || '0');
                
                if (isNaN(dataPrice) || (!isNaN(visiblePrice) && visiblePrice > 0 && dataPrice !== visiblePrice)) {
                    row.setAttribute('data-price', visiblePrice.toString());
                    console.log(`已將行的data-price從${dataPrice}更新為${visiblePrice}`);
                }
            }
            
            // 同步數量
            if (qtyElement) {
                const visibleQty = parseInt(qtyElement.textContent.trim());
                const dataQty = parseInt(row.getAttribute('data-quantity') || '0');
                
                if (isNaN(dataQty) || (!isNaN(visibleQty) && visibleQty > 0 && dataQty !== visibleQty)) {
                    row.setAttribute('data-quantity', visibleQty.toString());
                    console.log(`已將行的data-quantity從${dataQty}更新為${visibleQty}`);
                }
            }
            
            // 同步總價
            if (totalCell) {
                const visibleTotal = parseFloat(totalCell.textContent.replace('NT$', '').trim());
                const dataTotal = parseFloat(row.getAttribute('data-total') || '0');
                
                if (isNaN(dataTotal) || (!isNaN(visibleTotal) && visibleTotal > 0 && dataTotal !== visibleTotal)) {
                    row.setAttribute('data-total', visibleTotal.toString());
                    console.log(`已將行的data-total從${dataTotal}更新為${visibleTotal}`);
                }
            }
            
            // 2. 重新計算總價（如果價格和數量是有效的）
            const price = parseFloat(row.getAttribute('data-price') || '0');
            const quantity = parseInt(row.getAttribute('data-quantity') || '1');
            
            if (!isNaN(price) && !isNaN(quantity) && price > 0 && quantity > 0) {
                const calculatedTotal = price * quantity;
                
                // 更新data-total屬性
                row.setAttribute('data-total', calculatedTotal.toString());
                
                // 同時更新顯示的總價
                if (totalCell && totalCell.textContent !== `NT$${calculatedTotal}`) {
                    totalCell.textContent = `NT$${calculatedTotal}`;
                    console.log(`已更新顯示的總價為: NT$${calculatedTotal}`);
                }
            }
        });
        
        // 3. 重新計算表格總金額
        updateTableTotal(table);
    });
    
    return true; // 返回成功標誌
};
    
    // 更新表格總金額
    function updateTableTotal(table) {
        const rows = table.querySelectorAll('tbody tr');
        let total = 0;
        
        rows.forEach(row => {
            const itemTotal = parseFloat(row.getAttribute('data-total') || '0');
            if (!isNaN(itemTotal)) {
                total += itemTotal;
            }
        });
        
        // 更新表格底部的總計
        const totalElement = table.querySelector('tfoot strong');
        if (totalElement) {
            totalElement.textContent = `NT$${total}`;
            console.log('已更新表格總金額為:', total);
        }
    }
  // 設置定時執行同步
document.addEventListener('DOMContentLoaded', function() {
    console.log('表格數據同步腳本已載入');
    
    // 每2秒同步一次
    setInterval(window.syncTableDataAttributes, 2000);
    
    // 同時添加事件監聽器，在修改數量時立即同步
    document.addEventListener('click', function(event) {
        // 監聽增加/減少數量的按鈕點擊
        if (event.target.matches('.quantity-btn')) {
            // 延遲執行同步，等待數量更新完成
            setTimeout(window.syncTableDataAttributes, 100);
        }
        
        // 監聽結帳按鈕點擊
        if (event.target.matches('.checkout-btn') || 
            (event.target.parentElement && event.target.parentElement.matches('.checkout-btn'))) {
            console.log('準備結帳前同步數據');
            window.syncTableDataAttributes();
        }
    });
    
    // 頁面載入後立即執行一次同步
    setTimeout(window.syncTableDataAttributes, 1500);
});
