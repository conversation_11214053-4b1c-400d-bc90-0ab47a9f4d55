Feature: 自然言語順序付け 顧客として 自然言語（テキストまたは音声）を使って食べ物を注文したい。 それによって、素早く直感的に注文ができる

Scenario: 顧客はテキスト入力で注文する
  Given 顧客が注文ページにいる
  When 顧客は自然言語入力フィールドに「ビッグマックとコーンスープを注文したい」と入力する。
  Then システムは「ビッグマック エクストラバリューミール」と「コーンスープ」を識別する必要があります。
  And 「ビッグマック エクストラバリューミール」と「コーンスープ」の詳細（名前、価格、画像、在庫状況）をデータベース/RAGで照会します。
  And 識別された商品とその詳細を自然言語を使用して顧客に確認のために提示します（例：「ビッグマック エクストラバリューセットとコーンスープを注文しますか？」）

Scenario: 顧客は音声入力を使って食べ物を注文する
  Given 顧客が注文ページにいる
  When 顧客は音声入力を使用して「マッククリスピーチキン（2ピース）エクストラバリューミールを希望します」と言います。
  Then システムは音声入力をテキストに書き起こす必要がある
  And 「マッククリスピーチキン（2ピース）エクストラバリューミール」を特定する
  And 「マッククリスピーチキン（2ピース）エクストラバリューミール」の詳細（名前、価格、画像、在庫状況）をデータベース/RAGで照会します。
  And 識別した商品とその詳細を自然言語で顧客に提示し、確認します（例：「マッククリスピーチキン（2ピース）エクストラバリューミールをお選びになりましたか？」）

Scenario: 顧客が注文内容を変更する
  Given システムは確認のために識別された項目を提示しました
  When 顧客は「コーンスープをコーラに変更しました」と言います
  Then システムは提案された注文から「コーンスープ」を削除する必要がある
  And 「Coke」の詳細についてはデータベース/RAGに問い合わせてください
  And 変更した注文（ビッグマック エクストラバリューセット、コーラ）を自然言語で顧客に提示し、確認する

Scenario: システムが食事を認識できない
  Given 顧客が注文ページにいる
  When 顧客が「不思議な魔法のバーガーをください」と入力する
  Then システムは「ミステリアスマジックバーガー」というアイテムが見つからなかったことを示すはずです
  And メニューを確認するか、リクエストを言い換えることを提案する

Scenario: 顧客が注文を変更 - 商品を追加
  Given システムは確認のために識別された商品（ビッグマック エクストラバリューミール）を提示しました
  When 顧客は「フライドポテトを1つ追加してください」と言います
  Then システムは「フライドポテト」を識別する必要がある
  And 「フライドポテト」の詳細についてはデータベース/RAGに問い合わせてください
  And 提案された注文に「フライドポテト」を追加する
  And 確認のために自然言語を使用して、更新された注文（ビッグマック エクストラ バリュー ミール、フライドポテト）を顧客に提示します（例：「わかりました。フライドポテトを 1 つ追加しました。これでビッグマック エクストラ バリュー ミールとフライドポテト 1 つが含まれます。よろしいですか？」）

Scenario: 顧客が注文を変更 - 商品を削除
  Given システムは確認のために識別された商品（ビッグマック エクストラバリューミール、コーンスープ）を提示しました
  When 顧客は「コーンスープはもうありません」と言います
  Then システムは提案された注文から「コーンスープ」を削除する必要がある
  And 確認のために自然言語を使用して、更新された注文（ビッグマック エクストラバリューミール）を顧客に提示します（例：「わかりました。コーンスープは削除されました。現在はビッグマック エクストラバリューミールのみです。よろしいですか？」）

Scenario: 一部の食事はシステムで認識されません
  Given 顧客が注文ページにいる
  When 顧客が「チーズバーガーとパイが欲しい」と入力する
  Then システムは「チーズバーガー」を認識するはずだ
  And 「ある種のパイ」という項目が見つからなかったことを示す
  And メニューを確認するか、不明な項目のリクエストを言い換えることを提案する

Scenario: 食事に関する情報を尋ねる顧客
  Given 顧客が注文ページにいる
  When 顧客が「ビッグマックとは何ですか？」と入力します。
  Then システムはデータベース/RAG に「ビッグマック」の詳細 (説明、材料など) を照会する必要があります。
  And 「ビッグマック」に関する情報を自然言語を使用して顧客に提示する（例：「ビッグマックは、...を含むクラシックなダブルビーフバーガーです」）

Scenario: 顧客が注文を確認する
  Given システムは確認のための最終注文を提示しました(ビッグマックの余分な価値の食事、コーラ)
  When 顧客と話し、「確定」或「大丈夫だ、问题ない」
  Then システムは注文を処理すべきです
  And 注文確認メッセージ(例:「ご注文が送信されました!」)を表示します。

Scenario: お客様からキャンセル注文
  Given システムは確認のために識別された項目を提示しました(ビッグマックの余分な価値の食事、コーンスープ)
  When 顧客と話し、『キャンセル』或「もうそれはいらない」
  Then システムは現在の注文をキャンセルする必要があります
  And キャンセルを示すメッセージを表示します（例：「ご注文はキャンセルされました。」）