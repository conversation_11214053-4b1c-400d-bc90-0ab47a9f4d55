/**
 * JSON 解析修復中間件
 * 處理來自 LLM 的回應中可能包含 Markdown 代碼塊的 JSON
 */
import { Request, Response, NextFunction } from "express";

/**
 * 從回應字符串中提取 JSON
 * @param response 可能包含 JSON 的字符串
 * @returns 解析後的 JSON 對象，如果無法解析則返回 null
 */
export function extractJSON(response: string): any {
  try {
    // 首先嘗試直接解析
    return JSON.parse(response);
  } catch (e) {
    console.log("直接解析失敗，嘗試預處理...");
    console.log("原始回應:", response.substring(0, 100) + "...");
    console.log(
      "原始JSON解析錯誤:",
      e instanceof Error ? e.message : "未知錯誤",
    );

    // 0. 嘗試處理嵌套的 JSON
    try {
      const unnested = handleNestedGeminiJSON(response);
      if (unnested !== response) {
        console.log("發現並處理了嵌套 JSON");
        try {
          return JSON.parse(unnested);
        } catch (nestedError) {
          console.log("處理嵌套 JSON 後仍無法解析，繼續嘗試其他方法");
        }
      }
    } catch (nestedError) {
      console.log(
        "處理嵌套 JSON 失敗:",
        nestedError instanceof Error ? nestedError.message : "未知錯誤",
      );
    }

    // 1. 檢查是否包含 Markdown 代碼塊
    const markdownRegexes = [
      /```json\s*([\s\S]*?)\s*```/, // ```json 格式
      /```javascript\s*([\s\S]*?)\s*```/, // ```javascript 格式
      /```js\s*([\s\S]*?)\s*```/, // ```js 格式
      /```\s*([\s\S]*?)\s*```/, // 任何 ``` 格式
    ];

    for (const regex of markdownRegexes) {
      const match = response.match(regex);
      if (match && match[1]) {
        console.log("發現 Markdown 代碼塊，嘗試解析內容");
        const extracted = match[1].trim();

        try {
          return JSON.parse(extracted);
        } catch (innerError) {
          console.log("Markdown 代碼塊解析失敗，嘗試預處理內容...");
          // 嘗試預處理代碼塊內容
          const processed = preprocessJSON(extracted);
          try {
            const result = JSON.parse(processed);
            console.log("預處理後成功解析 Markdown 代碼塊內容");
            return result;
          } catch (processingError) {
            console.log(
              "預處理後仍無法解析代碼塊內容:",
              processingError instanceof Error
                ? processingError.message
                : "未知錯誤",
            );
            // 繼續嘗試下一個方法
          }
        }
      }
    }

    // 2. 預處理整個回應並嘗試解析
    const preprocessed = preprocessJSON(response);
    try {
      return JSON.parse(preprocessed);
    } catch {
      // 繼續嘗試其他方法
    }
    // 3. 使用正則表達式提取可能的 JSON 對象
    const jsonObjectRegex =
      /(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})/g;
    const matches = response.match(jsonObjectRegex);

    if (matches && matches.length > 0) {
      console.log(`找到 ${matches.length} 個可能的 JSON 對象，嘗試解析...`);
      for (const match of matches) {
        try {
          return JSON.parse(match);
        } catch (jsonError) {
          console.log("JSON 對象解析失敗，嘗試預處理...");
          // 嘗試預處理後解析
          try {
            const processedMatch = preprocessJSON(match);
            const result = JSON.parse(processedMatch);
            console.log("預處理後成功解析 JSON 對象");
            return result;
          } catch (processError) {
            console.log("預處理後仍無法解析 JSON 對象，嘗試下一個匹配");
            // 繼續嘗試下一個匹配
          }
        }
      }
    }

    // 4. 嘗試修復截斷的 JSON
    try {
      console.log("嘗試修復可能截斷的 JSON");
      const truncatedJSON = tryFixTruncatedJSON(response);
      if (truncatedJSON) {
        console.log("成功修復截斷的 JSON");
        return truncatedJSON;
      }
    } catch (error) {
      console.log(
        "修復截斷 JSON 失敗:",
        error instanceof Error ? error.message : "未知錯誤",
      );
    }

    // 如果所有方法都失敗，返回 null
    return null;
  }
}

/**
 * 嘗試修復截斷的 JSON
 * @param input 可能截斷的 JSON 字符串
 * @returns 修復後的 JSON 對象，如果無法修復則返回 null
 */
function tryFixTruncatedJSON(input: string): any {
  // 如果字符串為空，直接返回 null
  if (!input || input.trim() === "") {
    console.log("輸入為空，無法修復");
    return null;
  }

  try {
    console.log(`修復JSON: 嘗試修復截斷的 JSON，長度: ${input.length}`);
    // 檢查開頭是否是有效的 JSON 開始
    if (!input.trim().startsWith("{") && !input.trim().startsWith("[")) {
      console.log(
        "修復JSON: 輸入不是以 { 或 [ 開始，尋找第一個有效的 JSON 起始標記",
      );
      // 尋找第一個 { 或 [ 字符
      const startIndex = Math.min(
        input.indexOf("{") >= 0 ? input.indexOf("{") : Infinity,
        input.indexOf("[") >= 0 ? input.indexOf("[") : Infinity,
      );

      if (startIndex === Infinity) {
        console.log("修復JSON: 沒有找到有效的 JSON 開始標記");
        return null; // 沒有找到有效的 JSON 開始標記
      }

      console.log(`修復JSON: 找到有效的 JSON 起始位置在索引 ${startIndex}`);
      input = input.substring(startIndex);
    }

    // 去除註釋和其他干擾字符
    input = input.replace(/\/\*[\s\S]*?\*\//g, "").replace(/\/\/.*$/gm, "");

    // 處理可能由 Gemini 產生的含有 ... 的不完整 JSON
    input = input.replace(/\.\.\.\s*(?=[\]}])/g, '"[省略內容]"');

    // 計算花括號和方括號數量
    let openBraces = 0,
      closeBraces = 0;
    let openBrackets = 0,
      closeBrackets = 0;
    let inString = false;
    let escapeNext = false;

    // 更準確地計算括號數量，考慮字符串內容中的括號
    for (let i = 0; i < input.length; i++) {
      const char = input[i];

      if (escapeNext) {
        escapeNext = false;
        continue;
      }

      if (char === "\\") {
        escapeNext = true;
        continue;
      }

      if (char === '"') {
        inString = !inString;
        continue;
      }

      if (inString) continue; // 在字符串內的括號不計算

      if (char === "{") openBraces++;
      else if (char === "}") closeBraces++;
      else if (char === "[") openBrackets++;
      else if (char === "]") closeBrackets++;
    }

    console.log(
      `修復JSON: 括號計數 - {: ${openBraces}, }: ${closeBraces}, [: ${openBrackets}, ]: ${closeBrackets}`,
    );

    // 添加缺少的結束括號
    let processedInput = input;

    // 檢測 JSON 是否被截斷在屬性名稱或值中間
    const propertyMatch = processedInput.match(/"([^"]+)"\s*:\s*$/);
    if (propertyMatch) {
      console.log(`修復JSON: 發現截斷在屬性 "${propertyMatch[1]}" 後`);
      processedInput += ' "[省略內容]"';
    }

    // 檢測 JSON 是否被截斷在冒號後面
    const colonMatch = processedInput.match(/:\s*$/);
    if (colonMatch && !propertyMatch) {
      console.log("修復JSON: 發現截斷在冒號後面");
      processedInput += ' "[省略內容]"';
    }

    // 檢測 JSON 是否被截斷在逗號後面
    const commaMatch = processedInput.match(/,\s*$/);
    if (commaMatch) {
      console.log("修復JSON: 發現截斷在逗號後面");
      processedInput += ' "[省略內容]"';
    }

    // 如果花括號不平衡
    if (openBraces > closeBraces) {
      console.log(`修復JSON: 添加 ${openBraces - closeBraces} 個缺失的 } 括號`);
      for (let i = 0; i < openBraces - closeBraces; i++) {
        processedInput += "}";
      }
    }

    // 如果方括號不平衡
    if (openBrackets > closeBrackets) {
      console.log(
        `修復JSON: 添加 ${openBrackets - closeBrackets} 個缺失的 ] 括號`,
      );
      for (let i = 0; i < openBrackets - closeBrackets; i++) {
        processedInput += "]";
      }
    }

    // 修復不完整的鍵值對，例如結尾是 "key": 但沒有值
    processedInput = processedInput.replace(
      /"([^"]+)"\s*:\s*(?=[\]}])/g,
      '"$1":"[省略內容]"',
    );

    // 修復鍵名後面沒有值的情況
    processedInput = processedInput.replace(
      /"([^"]+)"\s*:(?!\s*[{\["0-9tfn\-])/g,
      '"$1":"[省略內容]"',
    );

    // 嘗試解析修復後的 JSON
    try {
      console.log("修復JSON: 嘗試解析修復後的 JSON");
      return JSON.parse(processedInput);
    } catch (error) {
      console.log("修復JSON: 首次解析失敗，嘗試進一步預處理");
      // 如果仍然無法解析，嘗試進一步預處理
      const furtherProcessed = preprocessJSON(processedInput);
      console.log("修復JSON: 進一步預處理完成，再次嘗試解析");
      try {
        return JSON.parse(furtherProcessed);
      } catch (secondError) {
        console.log(
          "修復JSON: 進一步預處理後仍解析失敗",
          secondError instanceof Error ? secondError.message : "未知錯誤",
        );

        // 最後嘗試移除尾部干擾字符
        // 尋找最後一個有效的 JSON 結束括號
        const lastBraceIndex = furtherProcessed.lastIndexOf("}");
        const lastBracketIndex = furtherProcessed.lastIndexOf("]");
        const lastIndex = Math.max(lastBraceIndex, lastBracketIndex);

        if (lastIndex > 0) {
          console.log(`修復JSON: 嘗試截斷在索引 ${lastIndex + 1} 處`);
          const truncated = furtherProcessed.substring(0, lastIndex + 1);
          try {
            return JSON.parse(truncated);
          } catch (e) {
            console.log("修復JSON: 所有修復嘗試都失敗");
          }
        }
      }
      return null;
    }
  } catch (error) {
    console.log(
      "修復截斷 JSON 失敗:",
      error instanceof Error ? error.message : "未知錯誤",
    );
    return null;
  }
}

/**
 * 預處理 JSON 字符串，修復常見的語法問題
 * @param input 輸入的 JSON 字符串
 * @returns 處理後的字符串
 */
function preprocessJSON(input: string): string {
  console.log("預處理JSON: 開始預處理，輸入長度:", input.length);
  if (input.length > 100) {
    console.log("預處理JSON: 輸入示例:", input.substring(0, 100) + "...");
  }

  // 處理 Markdown 和代碼塊格式
  let processedInput = input
    // 移除 Markdown 代碼塊標記
    .replace(/```(?:json|javascript|js)?\s*|\s*```/g, "");

  // 處理 Gemini API 特有的省略格式
  console.log("預處理JSON: 處理省略號和特殊格式");

  // 預先檢測特殊格式並記錄
  if (processedInput.includes("...")) {
    console.log("預處理JSON: 發現省略號，進行特殊處理");
  }

  // 處理 Gemini API 中常見的省略格式模式

  // 1. 處理包含省略號的不完整屬性
  processedInput = processedInput
    // 處理屬性值為省略號的情況
    .replace(/(".*?"\s*:\s*)\.\.\.(\s*[,}])/g, '$1"[省略內容]"$2')
    // 處理無引號屬性名稱後的省略號
    .replace(/(\w+\s*:\s*)\.\.\.(\s*[,}])/g, '$1"[省略內容]"$2');

  // 2. 處理 feature 對象的特殊格式
  processedInput = processedInput
    // 處理空的 feature 對象
    .replace(/"feature"\s*:\s*\{\s*\}/g, '"feature":{"name":"[空物件]"}')
    // 處理含省略號的 feature 對象
    .replace(
      /"feature"\s*:\s*\{\s*\.\.\.\s*\}/g,
      '"feature":{"name":"[省略內容]"}',
    );

  // 3. 處理 parameters 對象的特殊格式
  processedInput = processedInput
    // 處理 parameters 對象中的省略內容
    .replace(
      /"parameters"\s*:\s*\{\s*\.\.\.\s*\}/g,
      '"parameters":{"value":"[省略內容]"}',
    )
    // 處理 parameters 對象中的部分省略
    .replace(
      /"parameters"\s*:\s*\{([^{}]*?)(\.\.\.)([^{}]*?)\}/g,
      '"parameters":{"$1"[省略內容]"$3}',
    );

  // 4. 處理一般對象中的省略內容
  processedInput = processedInput
    // 處理其他對象中的省略內容
    .replace(
      /"([^"]+)"\s*:\s*\{\s*\.\.\.\s*\}/g,
      '"$1":{"value":"[省略內容]"}',
    );

  // 5. 處理不完整的數組
  processedInput = processedInput
    // 處理數組開頭或中間的省略
    .replace(/(\[\s*)\.\.\.(\s*,)/g, '$1"[省略內容]"$2')
    // 處理數組中間或結尾的省略
    .replace(/(,\s*)\.\.\.(\s*\])/g, '$1"[省略內容]"$2')
    // 處理只包含省略號的數組
    .replace(/\[\s*\.\.\.\s*\]/g, '["[省略內容]"]')
    // 處理數組中間的省略
    .replace(/(\[[^\]]*?)\.\.\.([^\[]*?\])/g, '$1"[省略內容]"$2');

  // 6. 處理嵌套的省略模式
  processedInput = processedInput
    // 處理嵌套對象中的省略
    .replace(/(\{\s*[^{}]*?)\.\.\.([^{}]*?\})/g, '$1"[省略內容]"$2');

  // 7. 處理其他省略號情況
  processedInput = processedInput
    // 替換任何剩餘的省略號
    .replace(/\.\.\./g, '"[省略內容]"');

  // 8. 處理特殊字符和格式問題
  console.log("預處理JSON: 處理特殊字符和格式問題");
  processedInput = processedInput
    // 處理特殊字符和換行
    .replace(/\r\n|\n|\r/g, "\\n")
    .replace(/\t/g, "\\t")
    // 去除註釋
    .replace(/\/\*[\s\S]*?\*\//g, "")
    .replace(/\/\/.*$/gm, "")
    // 修復未加引號的鍵名
    .replace(/(\{|\,)\s*([a-zA-Z0-9_]+)\s*\:/g, '$1"$2":')
    // 處理 Python 格式
    .replace(/:\s*None\b/g, ":null")
    .replace(/:\s*True\b/g, ":true")
    .replace(/:\s*False\b/g, ":false")
    // 修復引號問題
    .replace(/([{,]\s*)'([^']+)'\s*:/g, '$1"$2":')
    .replace(/:\s*'([^']*)'/g, ':"$1"')
    // 修復包含換行符的字符串
    .replace(/:\s*"([^"]*?)(?:\n|\r\n?)([^"]*?)"/g, ':"$1\\n$2"')
    // 修復尾部逗號
    .replace(/,(\s*[\}\]])/g, "$1")
    // 替換無效的控制字符
    .replace(/[\x00-\x1F]/g, " ");

  // 9. 處理空值的特殊情況
  processedInput = processedInput
    // 處理屬性名後面直接接逗號或結束花括號的情況
    .replace(/"([^"]+)"\s*:(\s*[,}])/g, '"$1":null$2');

  // 10. 處理錯誤的字面量語法
  processedInput = processedInput
    // 處理錯誤的 undefined 字面量
    .replace(/:\s*undefined\b/g, ":null")
    // 處理錯誤的函數字面量
    .replace(/:\s*function\s*\([^)]*\)\s*\{[^}]*\}/g, ':"[函數]"');

  // 最終修飾
  processedInput = processedInput.trim();

  console.log("預處理JSON: 完成預處理");
  if (processedInput.length > 100) {
    console.log(
      "預處理JSON: 處理後示例:",
      processedInput.substring(0, 100) + "...",
    );
  }

  return processedInput;
}

/**
 * 處理 Gemini API 嵌套 JSON 的特殊情況
 * Gemini 有時會在回應中生成嵌套的 JSON 字符串，這個函數試圖處理這種情況
 * @param input 可能包含嵌套 JSON 的字符串
 * @returns 處理後的字符串
 */
function handleNestedGeminiJSON(input: string): string {
  console.log("處理嵌套JSON: 檢查是否有嵌套 JSON 字符串");

  // 檢查是否包含轉義的 JSON 字符串
  const escapedJsonRegex = /"(\\{[^"\\]*\\}|\\[[^"\\]*\\])"/g;
  let hasNestedJson = escapedJsonRegex.test(input);

  if (!hasNestedJson) {
    // 檢查是否包含引號內的 JSON 字面量
    const quotedJsonRegex = /"(\{[^"]*\}|\[[^"]*\])"/g;
    hasNestedJson = quotedJsonRegex.test(input);
  }

  if (!hasNestedJson) {
    return input; // 沒有嵌套 JSON，直接返回
  }

  console.log("處理嵌套JSON: 發現可能的嵌套 JSON，進行處理");

  // 處理轉義的 JSON 字符串
  let processedInput = input.replace(
    /"(\\{[^"\\]*\\})"/g,
    function (match, p1) {
      try {
        // 嘗試將轉義的 JSON 解析為對象
        const unescaped = p1.replace(/\\/g, "");
        JSON.parse(unescaped); // 驗證是有效的 JSON
        return unescaped; // 如果是有效的 JSON，去除引號
      } catch (e) {
        return match; // 如果無法解析，保持原樣
      }
    },
  );

  // 處理引號內的 JSON 字面量
  processedInput = processedInput.replace(
    /"(\{[^"]*\})"/g,
    function (match, p1) {
      try {
        JSON.parse(p1); // 驗證是有效的 JSON
        return p1; // 如果是有效的 JSON，去除引號
      } catch (e) {
        return match; // 如果無法解析，保持原樣
      }
    },
  );

  // 處理引號內的 JSON 數組
  processedInput = processedInput.replace(
    /"(\[[^"]*\])"/g,
    function (match, p1) {
      try {
        JSON.parse(p1); // 驗證是有效的 JSON
        return p1; // 如果是有效的 JSON，去除引號
      } catch (e) {
        return match; // 如果無法解析，保持原樣
      }
    },
  );

  return processedInput;
}

/**
 * JSON 解析修復中間件
 * 攔截 JSON 響應體，確保可以正確解析帶有 Markdown 代碼塊的 JSON
 */
export function jsonParserFix(req: Request, res: Response, next: NextFunction) {
  // 儲存原始的 res.json 方法
  const originalJson = res.json;

  // 包裝 res.json 方法，處理可能的 Markdown 代碼塊
  res.json = function (body: any) {
    // 記錄請求路徑
    const requestPath = req.path || "unknown path";

    // 如果 body 是字符串，嘗試解析其中的 JSON
    if (typeof body === "string") {
      console.log(
        `[JSON Parser] 路徑: ${requestPath} - 收到字符串類型的回應，長度: ${body.length} 字節`,
      );

      try {
        // 保存原始回應以備參考
        const originalResponse = body;

        // 嘗試提取和解析 JSON
        const parsedBody = extractJSON(body);

        if (parsedBody) {
          console.log(
            `[JSON Parser] 路徑: ${requestPath} - 成功解析 JSON 回應`,
          );
          return originalJson.call(res, parsedBody);
        } else {
          console.log(
            `[JSON Parser] 路徑: ${requestPath} - 無法從字符串中提取 JSON，返回原始回應`,
          );

          // 如果無法解析，提供更有用的回應而不是原始字符串
          const errorResponse = {
            success: false,
            error: "無法解析回應為有效的 JSON",
            message: "服務回應了無效的格式",
            // 只傳回部分原始文本以避免過大的回應
            originalTextSample:
              originalResponse.substring(0, 500) +
              (originalResponse.length > 500 ? "..." : ""),
          };

          return originalJson.call(res, errorResponse);
        }
      } catch (error) {
        console.error(
          `[JSON Parser] 路徑: ${requestPath} - 解析 JSON 時發生錯誤:`,
          error,
        );

        // 回傳友好的錯誤訊息，而不是原始錯誤
        const errorResponse = {
          success: false,
          error: "處理 JSON 響應時出錯",
          message: error instanceof Error ? error.message : "未知錯誤",
          // 只傳回部分原始文本以避免過大的回應
          originalTextSample:
            body.length > 500 ? body.substring(0, 500) + "..." : body,
        };

        return originalJson.call(res, errorResponse);
      }
    } else if (body === null || body === undefined) {
      console.log(
        `[JSON Parser] 路徑: ${requestPath} - 收到空回應 (${body === null ? "null" : "undefined"})`,
      );
      // 處理空回應
      return originalJson.call(res, {
        success: false,
        error: "空回應",
        message: "服務返回了空值",
      });
    } else if (typeof body === "object") {
      // 物件類型回應無需處理
      return originalJson.call(res, body);
    }

    // 對於其他類型，轉換為字符串後使用原始的 json 方法
    console.log(
      `[JSON Parser] 路徑: ${requestPath} - 收到未知類型的回應: ${typeof body}`,
    );
    return originalJson.call(res, body);
  };

  next();
}

export default jsonParserFix;
