# 使用最新的 Node.js 20 LTS 版本作為基礎映像，修復安全漏洞
# 改用更安全的 slim 版本而非 alpine，以減少安全漏洞
FROM node:20-slim

# 更新系統套件並安裝必要的安全工具
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    dumb-init \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 設定工作目錄
WORKDIR /app

# 首先只複製 package 文件以利用 Docker 快取
COPY package*.json ./

# 安裝依賴，使用 npm audit fix 修復已知漏洞
RUN npm ci --only=production && \
    npm audit fix --force || true && \
    npm cache clean --force

# 複製 TypeScript 配置文件
COPY tsconfig.json ./

# 複製源代碼
COPY src/ ./src/

# 複製靜態文件
COPY public/ ./public/

# 複製預設的 BDD 和 AAprompt 文件
COPY BDD_*.txt ./
COPY AAprompt_*.txt ./

# 安裝 TypeScript 編譯器（開發依賴）
RUN npm install -g typescript

# 編譯 TypeScript
RUN npm run build

# 創建必要的目錄並設定權限
RUN mkdir -p /app/uploads /app/appPrompt && \
    chmod 755 /app/uploads /app/appPrompt

# 創建非 root 用戶
RUN groupadd -r nodejs --gid=1001 && \
    useradd -r -g nodejs --uid=1001 --home-dir=/app --shell=/bin/bash nextjs

# 設定目錄擁有者
RUN chown -R nextjs:nodejs /app/uploads /app/appPrompt

# 切換到非 root 用戶
USER nextjs

# 暴露端口
EXPOSE 3005

# 設定環境變數
ENV NODE_ENV=production
ENV PORT=3005

# 啟動應用（使用 dumb-init 提高安全性）
ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]