Feature: 自然語言點餐 As a customer I want to order food using natural language (text or voice) So that I can place my order quickly and intuitively

Scenario: 顧客使用文字輸入點餐
  Given a customer is on the ordering page
  When the customer enters "我想點一個大麥克套餐和一杯玉米湯" into the natural language input field
  Then the system should identify "大麥克套餐" and "玉米湯"
  And query the database/RAG for details (name, price, image, availability) of "大麥克套餐" and "玉米湯"
  And present the identified items and their details back to the customer using natural language for confirmation (e.g., "您是想點一份大麥克套餐和一份玉米湯嗎？")

Scenario: 顧客使用語音輸入點餐
  Given a customer is on the ordering page
  When the customer uses voice input saying "我要一個麥脆雞兩塊餐"
  Then the system should transcribe the voice input to text
  And identify "麥脆雞兩塊餐"
  And query the database/RAG for details (name, price, image, availability) of "麥脆雞兩塊餐"
  And present the identified item and its details back to the customer using natural language for confirmation (e.g., "您選的是一份麥脆雞兩塊餐，對嗎？")

Scenario: 顧客修改點餐內容
  Given the system has presented the identified items for confirmation
  When the customer says "玉米湯換成可樂"
  Then the system should remove "玉米湯" from the proposed order
  And query the database/RAG for details of "可樂"
  And present the modified order (大麥克套餐, 可樂) back to the customer using natural language for confirmation

Scenario: 系統無法識別餐點
  Given a customer is on the ordering page
  When the customer enters "來一份神秘魔法漢堡"
  Then the system should indicate that the item "神秘魔法漢堡" was not found
  And suggest checking the menu or rephrasing the request

Scenario: 顧客修改點餐內容 - 增加品項
  Given the system has presented the identified items for confirmation (大麥克套餐)
  When the customer says "再加一份薯條"
  Then the system should identify "薯條"
  And query the database/RAG for details of "薯條"
  And add "薯條" to the proposed order
  And present the updated order (大麥克套餐, 薯條) back to the customer using natural language for confirmation (e.g., "好的，幫您加了一份薯條，現在是包含大麥克套餐和薯條，請問確認嗎？")

Scenario: 顧客修改點餐內容 - 移除品項
  Given the system has presented the identified items for confirmation (大麥克套餐, 玉米湯)
  When the customer says "玉米湯不要了"
  Then the system should remove "玉米湯" from the proposed order
  And present the updated order (大麥克套餐) back to the customer using natural language for confirmation (e.g., "好的，已經移除玉米湯，目前是只有大麥克套餐，請問確認嗎？")

Scenario: 系統無法識別部分餐點
  Given a customer is on the ordering page
  When the customer enters "我要一個吉士漢堡和一個不知道什麼派"
  Then the system should identify "吉士漢堡"
  And indicate that the item "不知道什麼派" was not found
  And suggest checking the menu or rephrasing the request for the unknown item

Scenario: 顧客詢問餐點資訊
  Given a customer is on the ordering page
  When the customer enters "大麥克是什麼"
  Then the system should query the database/RAG for details of "大麥克" (description, ingredients, etc.)
  And present the information about "大麥克" to the customer using natural language (e.g., "大麥克是一款經典的雙層牛肉漢堡，包含...")

Scenario: 顧客確認訂單
  Given the system has presented the final order for confirmation (大麥克套餐, 可樂)
  When the customer says "確認" 或 "沒問題"
  Then the system should process the order
  And display an order confirmation message (e.g., "您的訂單已送出！")

Scenario: 顧客取消訂單
  Given the system has presented the identified items for confirmation (大麥克套餐, 玉米湯)
  When the customer says "取消" 或 "我不要了"
  Then the system should cancel the current order
  And display a message indicating the cancellation (e.g., "好的，您的訂單已取消。")