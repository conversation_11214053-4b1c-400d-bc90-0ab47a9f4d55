/**
 * 語音識別服務
 * 使用 Web Speech API 進行語音轉文字處理
 */

// 聲明 window 上的 webkitSpeechRecognition 類型
declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
}

// 語音識別結果類型
export interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

// 語音識別選項
export interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

/**
 * 語音識別服務類
 * 提供語音轉文字和語音識別相關功能
 */
export class SpeechRecognitionService {
  private recognition: any;
  private isListening: boolean = false;
  private noiseFilter: NoiseFilter;
  
  constructor() {
    // 建立瀏覽器相容的語音識別物件
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      this.recognition = new SpeechRecognition();
      this.setupRecognition();
    } else {
      console.error('您的瀏覽器不支援語音識別功能');
    }
    
    // 初始化噪音過濾器
    this.noiseFilter = new NoiseFilter();
  }
  
  /**
   * 設定語音識別的基本參數
   */
  private setupRecognition() {
    if (!this.recognition) return;
    
    this.recognition.lang = 'zh-TW';
    this.recognition.continuous = false;
    this.recognition.interimResults = true;
    this.recognition.maxAlternatives = 1;
  }
  
  /**
   * 開始語音識別
   * @param options 語音識別選項
   * @param onResult 結果回調
   * @param onError 錯誤回調
   */
  startListening(
    options: SpeechRecognitionOptions = {},
    onResult: (result: SpeechRecognitionResult) => void,
    onError: (error: Error) => void
  ): boolean {
    if (!this.recognition) {
      onError(new Error('您的瀏覽器不支援語音識別功能'));
      return false;
    }
    
    if (this.isListening) {
      this.stopListening();
    }
    
    // 應用選項
    if (options.language) {
      this.recognition.lang = options.language;
    }
    
    if (options.continuous !== undefined) {
      this.recognition.continuous = options.continuous;
    }
    
    if (options.interimResults !== undefined) {
      this.recognition.interimResults = options.interimResults;
    }
    
    if (options.maxAlternatives !== undefined) {
      this.recognition.maxAlternatives = options.maxAlternatives;
    }
    
    // 設置結果和錯誤處理
    this.recognition.onresult = (event: any) => {
      const result = event.results[event.resultIndex];
      const transcript = result[0].transcript;
      
      // 應用噪音過濾
      const filteredText = this.noiseFilter.filterNoise(transcript);
      
      onResult({
        transcript: filteredText,
        confidence: result[0].confidence,
        isFinal: result.isFinal
      });
    };
    
    this.recognition.onerror = (event: any) => {
      onError(new Error(`語音識別錯誤: ${event.error}`));
    };
    
    this.recognition.onend = () => {
      this.isListening = false;
    };
    
    // 開始識別
    try {
      this.recognition.start();
      this.isListening = true;
      return true;
    } catch (error) {
      onError(error as Error);
      return false;
    }
  }
  
  /**
   * 停止語音識別
   */
  stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
      this.isListening = false;
    }
  }
  
  /**
   * 檢查瀏覽器是否支援語音識別
   */
  isSupported(): boolean {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
  }
  
  /**
   * 獲取當前是否正在進行語音識別
   */
  isCurrentlyListening(): boolean {
    return this.isListening;
  }
}

/**
 * 噪音過濾器類
 * 用於過濾語音識別中的噪音和不相關內容
 */
class NoiseFilter {
  // 噪音字詞列表
  private noiseWords = ['嗯', '啊', '呃', '這個', '那個', '就是', '那麼', '所以'];
  
  // 重複詞過濾閾值
  private repeatThreshold = 3;
  
  /**
   * 過濾語音識別結果中的噪音
   * @param text 原始識別文本
   * @returns 過濾後的文本
   */
  filterNoise(text: string): string {
    if (!text) return '';
    
    // 步驟 1: 移除噪音詞彙
    let filtered = this.removeNoiseWords(text);
    
    // 步驟 2: 移除過多的重複詞
    filtered = this.removeExcessiveRepeats(filtered);
    
    // 步驟 3: 修正常見的語音識別錯誤
    filtered = this.fixCommonMistakes(filtered);
    
    return filtered.trim();
  }
  
  /**
   * 移除噪音詞彙
   */
  private removeNoiseWords(text: string): string {
    let filtered = text;
    
    this.noiseWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'g');
      filtered = filtered.replace(regex, '');
    });
    
    return filtered;
  }
  
  /**
   * 移除過多的重複詞
   */
  private removeExcessiveRepeats(text: string): string {
    // 將文本分割成詞彙
    const words = text.split(/\s+/);
    const result: string[] = [];
    
    let prevWord = '';
    let repeatCount = 0;
    
    // 檢測和處理重複
    words.forEach(word => {
      if (word === prevWord) {
        repeatCount++;
        if (repeatCount < this.repeatThreshold) {
          result.push(word);
        }
      } else {
        result.push(word);
        prevWord = word;
        repeatCount = 1;
      }
    });
    
    return result.join(' ');
  }
  
  /**
   * 修正常見的語音識別錯誤
   */
  private fixCommonMistakes(text: string): string {
    // 這裡可以添加特定領域的常見錯誤修正
    // 例如菜單項目的常見錯誤
    const corrections: Record<string, string> = {
      '漢堡包': '漢堡',
      '可口可樂': '可樂',
      '麥當勞': '麥當勞',
      '薯條': '薯條'
    };
    
    let correctedText = text;
    
    Object.entries(corrections).forEach(([wrong, correct]) => {
      const regex = new RegExp(wrong, 'g');
      correctedText = correctedText.replace(regex, correct);
    });
    
    return correctedText;
  }
}
