# Docker 部署指南

本指南說明如何使用 Docker 來部署和運行自然語言點餐系統（包含最新的會話隔離功能）。

## 🎯 最新更新 (2025-06-04)

### ✨ 新功能
- ✅ **多用戶會話隔離**：支援無限多用戶同時使用，互不干擾
- ✅ **純前端會話管理**：適合 Docker 部署的無狀態設計
- ✅ **會話狀態顯示**：實時顯示當前會話狀態
- ✅ **並發測試支援**：包含完整的多用戶測試頁面

### 🔧 改進
- ✅ **UI 優化**：隱藏了不必要的測試語音按鈕
- ✅ **預設文件支援**：包含多語系 BDD 和 AAprompt 預設內容
- ✅ **端口標準化**：生產環境使用 3005 端口，開發環境使用 3004 端口
- ✅ **Docker 優化**：更新了構建腳本和部署流程

### 🧪 測試功能
- 📋 **會話測試頁面**：http://localhost:3005/session-test.html
- 🔄 **自動化測試**：包含並發會話隔離測試腳本

## 📋 前置需求

- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用磁碟空間

## 🚀 快速開始

### 方法一：使用自動化腳本（推薦）

我們提供了便捷的構建腳本：

**Linux/macOS:**
```bash
# 使用互動式構建腳本
./docker-build.sh

# 或快速重新構建
./rebuild-docker.sh
```

**Windows PowerShell:**
```powershell
# 使用互動式構建腳本
.\docker-build.ps1

# 或快速重新構建
.\rebuild-docker.ps1
```

### 方法二：手動配置

### 1. 環境配置

首先複製環境變數範例文件：

```bash
cp .env.example .env
```

**重要：Gemini AI 配置**

系統需要 Google Gemini AI API 才能正常運行。請：

1. 前往 [Google AI Studio](https://makersuite.google.com/app/apikey) 獲取 API Key
2. 在 `.env` 文件中設定：
   ```
   GEMINI_API_KEY=your-real-api-key-here
   USE_GEMINI_AI=true
   ```

編輯 `.env` 文件，填入您的配置：

```env
# Gemini AI 配置 (必須)
GEMINI_API_KEY=your-real-api-key-here
USE_GEMINI_AI=true

# Firebase 配置
FIREBASE_API_KEY=your-actual-api-key
FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
FIREBASE_APP_ID=your-app-id

# 伺服器配置
PORT=3005
NODE_ENV=production

# 跨域設定
CORS_ORIGIN=*

# 默認語系
DEFAULT_LANGUAGE=zh-TW
```

### 2. 使用 Docker Compose 部署（推薦）

```bash
# 構建並啟動服務
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f
```

### 3. 使用 Docker 直接部署

```bash
# 構建映像
docker build -t natural-order .

# 創建數據目錄
mkdir -p uploads appPrompt

# 運行容器
docker run -d \
  --name natural-order-app \
  -p 3005:3005 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/appPrompt:/app/appPrompt \
  --env-file .env \
  natural-order
```

## 📁 數據持久化

系統使用兩個重要的數據目錄：

- **`uploads/`** - 儲存上傳的菜單檔案
- **`appPrompt/`** - 儲存生成的 APPprompt 文件

這些目錄通過 Docker 卷掛載到主機，確保數據在容器重啟後不會丟失。

### 權限設定

容器內使用非 root 用戶 `nextjs` (UID: 1001) 運行應用，確保安全性。如果遇到權限問題，可以調整主機目錄權限：

```bash
# 設定目錄權限
sudo chown -R 1001:1001 uploads appPrompt
sudo chmod -R 755 uploads appPrompt
```

## 🔧 管理命令

### 查看服務狀態

```bash
# 查看容器狀態
docker-compose ps

# 查看健康檢查狀態
docker-compose exec natural-order wget --spider http://localhost:3005
```

### 查看日誌

```bash
# 查看即時日誌
docker-compose logs -f natural-order

# 查看最近 100 行日誌
docker-compose logs --tail=100 natural-order
```

### 重啟服務

```bash
# 重啟服務
docker-compose restart natural-order

# 重新構建並重啟
docker-compose up -d --build
```

### 停止服務

```bash
# 停止服務
docker-compose stop

# 停止並移除容器
docker-compose down

# 停止並移除容器和映像
docker-compose down --rmi all
```

## 🌐 訪問應用

服務啟動後，可以通過以下地址訪問：

### 主要功能頁面
- **主頁面**: http://localhost:3005
- **菜單管理**: http://localhost:3005/index.html#menu-management
- **BDD 編輯器**: http://localhost:3005/index.html#bdd-editor
- **AAprompt 編輯器**: http://localhost:3005/index.html#aaprompt-editor
- **APPprompt 生成器**: http://localhost:3005/index.html#appprompt-generator
- **自然語言點餐**: http://localhost:3005/index.html#natural-language-ordering

### 🧪 測試和驗證頁面
- **會話隔離測試**: http://localhost:3005/session-test.html
  - 模擬多用戶同時使用
  - 驗證會話隔離效果
  - 並發測試功能

### 🔍 驗證部署成功

1. **基本功能測試**：
   - 訪問主頁面，確認頁面正常載入
   - 檢查會話狀態顯示（頁面頂部）
   - 確認語音測試按鈕已隱藏

2. **會話隔離測試**：
   - 訪問 http://localhost:3005/session-test.html
   - 執行批量測試，驗證多用戶隔離功能

3. **API 健康檢查**：
   ```bash
   curl http://localhost:3005/
   ```

## 🔍 故障排除

### 常見問題

1. **容器無法啟動**
   ```bash
   # 檢查日誌
   docker-compose logs natural-order
   
   # 檢查環境變數
   docker-compose config
   ```

2. **無法訪問應用**
   ```bash
   # 檢查端口是否被佔用
   netstat -tulpn | grep 3005
   
   # 檢查防火牆設定
   sudo ufw status
   ```

3. **文件上傳失敗**
   ```bash
   # 檢查目錄權限
   ls -la uploads/ appPrompt/
   
   # 修復權限
   sudo chown -R 1001:1001 uploads appPrompt
   ```

4. **Firebase 連接失敗**
   - 確認 `.env` 文件中的 Firebase 配置正確
   - 檢查網路連接
   - 驗證 Firebase 專案設定

### 健康檢查

系統包含內建的健康檢查機制：

```bash
# 手動執行健康檢查
docker-compose exec natural-order wget --spider http://localhost:3005

# 查看健康狀態
docker inspect natural-order-app | grep Health -A 10
```

## 🔒 安全考量

1. **非 root 用戶**: 容器內使用非特權用戶運行
2. **環境變數**: 敏感信息通過環境變數管理
3. **網路隔離**: 使用 Docker 網路進行服務隔離
4. **文件權限**: 適當的文件和目錄權限設定

## 📊 監控和維護

### 資源監控

```bash
# 查看容器資源使用情況
docker stats natural-order-app

# 查看磁碟使用情況
du -sh uploads/ appPrompt/
```

### 備份數據

```bash
# 備份上傳文件和生成的 prompt
tar -czf backup-$(date +%Y%m%d).tar.gz uploads/ appPrompt/
```

### 更新應用

```bash
# 拉取最新代碼
git pull

# 重新構建並部署
docker-compose up -d --build
```

## 🚀 生產環境部署建議

1. **使用反向代理**: 配置 Nginx 或 Apache 作為反向代理
2. **SSL 證書**: 配置 HTTPS 加密
3. **日誌管理**: 配置日誌輪轉和集中管理
4. **監控告警**: 設定服務監控和告警機制
5. **備份策略**: 定期備份數據和配置
6. **資源限制**: 設定容器資源限制

```yaml
# docker-compose.yml 生產環境配置範例
services:
  natural-order:
    # ... 其他配置
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```