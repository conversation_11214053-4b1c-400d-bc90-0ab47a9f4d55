# 啟動自然語言點餐系統
# 這個腳本會安裝所有依賴並啟動開發服務器

# 檢查 Node.js 是否已安裝
if (!(Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 Node.js。請先安裝 Node.js: https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# 檢查 npm 是否已安裝
if (!(Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "錯誤: 未找到 npm。請重新安裝 Node.js 以包含 npm。" -ForegroundColor Red
    exit 1
}

Write-Host "正在安裝依賴..." -ForegroundColor Cyan
npm install

# 檢查 .env 文件是否存在，如果不存在則從範例創建
if (!(Test-Path .env)) {
    Write-Host "創建 .env 文件從 .env.example..." -ForegroundColor Yellow
    if (Test-Path .env.example) {
        Copy-Item .env.example -Destination .env
        Write-Host "已創建 .env 文件。請編輯該文件添加您的 Firebase 配置信息。" -ForegroundColor Yellow
    } 
    else {
        Write-Host "警告: .env.example 文件未找到，請手動創建 .env 文件。" -ForegroundColor Yellow
    }
}

Write-Host "`n自然語言點餐系統啟動中...`n" -ForegroundColor Green
Write-Host "系統將在 http://localhost:3005 上運行" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服務器`n" -ForegroundColor Green

# 檢查是否安裝了tsx
$tsxInstalled = npm list -g | Select-String "tsx"
if (!$tsxInstalled) {
    Write-Host "安裝tsx套件中..." -ForegroundColor Yellow
    npm install -g tsx
}

# 啟動開發服務器
npm run dev
