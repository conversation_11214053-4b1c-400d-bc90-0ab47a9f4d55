import express from 'express';

const router = express.Router();

// 接收並記錄客戶端日誌的端點
router.post('/client-log', (req, res) => {
    const { message, data, level = 'info' } = req.body;
    
    // 格式化日誌消息
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    // 根據日誌級別選擇不同的輸出方式
    switch (level.toLowerCase()) {
        case 'error':
            console.error(logEntry, data || '');
            break;
        case 'warn':
            console.warn(logEntry, data || '');
            break;
        case 'debug':
            console.debug(logEntry, data || '');
            break;
        case 'info':
        default:
            console.log(logEntry, data || '');
            break;
    }
    
    res.status(200).json({ success: true });
});

export default router;
