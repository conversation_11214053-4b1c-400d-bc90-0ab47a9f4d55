# 自然語言點餐系統開發計畫

## 專案概述

基於 AI-OS Architecture Agent 方法論，開發一個通用的線上點餐系統，支援自然語言點餐功能（文字和語音輸入）。系統將提供 GUI 介面讓用戶自行輸入 BDD 與 AAprompt，並自動生成相對應的 APPprompt。

## 系統架構

### 核心組件
1. **BDD/AAprompt 編輯器** - GUI 介面讓用戶輸入規格
2. **APPprompt 生成引擎** - 自動生成應用程式提示
3. **菜單管理系統** - 支援 CSV/JSON 格式菜單上傳和管理
4. **自然語言處理模組** - 處理顧客輸入
5. **RAG/資料庫查詢模組** - 查詢餐點資訊
6. **自然語言回應模組** - 生成確認回覆
7. **前端點餐介面** - 顧客互動界面

## 開發階段

### 第一階段：BDD 與 AAprompt 標準化設計

#### 1.0 菜單數據標準化

**支援的菜單格式：**
```csv
// 基本餐點格式
"id","name_en","name_zh","price","category","description","image_url","availability"

// 飲料格式（包含尺寸）
"id","name_en","name_zh","price","size_en","size_zh","category"

// 套餐格式（包含組合項目）
"id","name_en","name_zh","price","main_item","side_item","drink_item"
```

**菜單數據結構範例：**
基於現有的 McDonald's 菜單數據，系統需要支援：
- **漢堡類 (burgers.csv)**: id, name_en, name_zh, price
- **飲料類 (drinks.csv)**: id, name_en, name_zh, price, size_en, size_zh  
- **套餐類 (extravaluemeal.csv)**: id, name_en, name_zh, price
- **配菜類 (sides.csv)**: id, name_en, name_zh, price

**數據驗證規則：**
- 必填欄位：id, name_zh, price
- 價格格式：正數，支援小數點
- 名稱：中英文雙語支援
- 分類：自動識別或手動指定

#### 1.1 BDD 模板設計

```gherkin
Feature: 自然語言點餐系統
  As a customer
  I want to order food using natural language (text or voice)
  So that I can place my order quickly and intuitively

Scenario: 文字輸入點餐
  Given 顧客在點餐頁面
  When 顧客輸入 "{natural_language_input}" 到自然語言輸入欄位
  Then 系統應該識別餐點項目 "{menu_items}"
  And 透過 RAG/資料庫查詢餐點詳細資訊 (名稱、價格、圖片、可用性)
  And 以自然語言方式呈現識別結果給顧客確認

Scenario: 語音輸入點餐
  Given 顧客在點餐頁面
  When 顧客使用語音輸入說 "{voice_input}"
  Then 系統應該將語音轉換為文字
  And 識別餐點項目 "{menu_items}"
  And 透過 RAG/資料庫查詢餐點詳細資訊
  And 以自然語言方式呈現識別結果給顧客確認

Scenario: 修改點餐內容
  Given 系統已呈現識別的餐點供確認
  When 顧客說 "{modification_request}"
  Then 系統應該更新訂單內容
  And 查詢新餐點的詳細資訊
  And 呈現修改後的訂單供確認

Scenario: 無法識別餐點
  Given 顧客在點餐頁面
  When 顧客輸入無法識別的餐點名稱
  Then 系統應該提示找不到該餐點
  And 建議查看菜單或重新表達需求

Scenario: 管理員上傳菜單
  Given 管理員在菜單管理頁面
  When 管理員上傳 CSV 格式的菜單檔案
  Then 系統應該驗證檔案格式和數據完整性
  And 解析並儲存菜單數據到資料庫
  And 建立餐點名稱的向量索引以支援 RAG 搜尋
  And 顯示上傳成功訊息和菜單預覽

Scenario: 菜單數據更新
  Given 系統已有現有菜單數據
  When 管理員上傳新的菜單檔案
  Then 系統應該比較新舊數據差異
  And 提示管理員確認更新內容
  And 備份原有數據後進行更新
  And 重新建立搜尋索引
```

#### 1.2 AAprompt 模板設計

```
AAprompt = [Attention] + [Action]

[Attention] - 系統需要關注的資訊：
• 顧客輸入: {customer_input} - 自然語言表達的點餐需求
• 餐點資料庫: {menu_database} - 餐點名稱、價格、圖片、描述、可用性
• 上傳菜單數據: {uploaded_menu_data} - 用戶上傳的 CSV 菜單檔案內容
• 當前菜單: {current_menu} - 當前可用餐點清單
• 餐點分類: {menu_categories} - 漢堡、飲料、套餐、配菜等分類
• 多語言支援: {language_mappings} - 中英文名稱對應
• 顧客偏好: {customer_preferences} - 歷史訂單和偏好
• 庫存資訊: {inventory_status} - 餐點可用性
• 促銷活動: {current_promotions} - 適用的優惠活動
• 時間因素: {time_context} - 早餐/午餐/晚餐時段
• 購物車: {cart_contents} - 已選餐點

[Action] - 系統需要執行的動作：
1. 解析和驗證上傳的菜單 CSV 檔案
2. 建立多語言餐點名稱索引和向量搜尋
3. 解析自然語言輸入，識別意圖和餐點詞彙
4. 透過 RAG/SQL 查詢餐點詳細資訊
5. 處理模糊輸入，向顧客澄清
6. 構建建議訂單草稿
7. 檢查餐點可用性和分類匹配
8. 生成自然語言確認回覆
9. 處理訂單修改指令
10. 提供基於菜單的個性化推薦
11. 完成訂單並加入購物車
12. 管理菜單版本和更新歷史
```

### 第二階段：GUI 介面開發

#### 2.1 BDD/AAprompt 編輯器介面

**功能需求：**
- BDD 編輯器：語法高亮、自動完成、驗證
- AAprompt 編輯器：結構化輸入表單
- 即時預覽功能
- 模板庫管理
- 匯入/匯出功能

**技術規格：**
- 前端框架：React/Vue.js
- 編輯器組件：Monaco Editor 或 CodeMirror
- UI 庫：Material-UI 或 Ant Design
- 狀態管理：Redux/Vuex

#### 2.2 菜單管理介面

**功能需求：**
- CSV 檔案上傳（支援拖拽）
- 菜單數據預覽和編輯
- 數據驗證和錯誤提示
- 分類管理（漢堡、飲料、套餐、配菜）
- 批量編輯和匯出
- 菜單版本管理和回復
- 搜尋和篩選功能

**支援的檔案格式：**
```typescript
interface MenuUploadFormat {
  fileTypes: ['csv', 'xlsx', 'json'];
  maxFileSize: '10MB';
  requiredColumns: ['id', 'name_zh', 'price'];
  optionalColumns: ['name_en', 'category', 'description', 'image_url'];
}
```

**數據驗證規則：**
- ID 唯一性檢查
- 價格數值驗證（正數）
- 必填欄位完整性
- 字元編碼檢查（UTF-8）
- 重複項目檢測

#### 2.3 APPprompt 生成器介面

**功能需求：**
- 輸入 BDD 和 AAprompt
- 自動生成 APPprompt
- 生成結果預覽
- 下載/複製功能
- 歷史記錄管理

### 第三階段：APPprompt 生成引擎開發

#### 3.1 生成邏輯設計

```javascript
// APPprompt 生成流程
function generateAPPprompt(bdd, aaprompt, menuData) {
  // 1. 解析 BDD 場景
  const scenarios = parseBDD(bdd);
  
  // 2. 提取 AAprompt 要素
  const attention = extractAttention(aaprompt);
  const actions = extractActions(aaprompt);
  
  // 3. 分析菜單數據結構
  const menuSchema = analyzeMenuStructure(menuData);
  
  // 4. 生成對應的應用程式提示
  const appprompt = {
    menu_processing: generateMenuProcessingPrompt(menuSchema),
    nlp_module: generateNLPPrompt(scenarios, attention, menuSchema),
    query_module: generateQueryPrompt(actions, menuSchema),
    response_module: generateResponsePrompt(scenarios, menuSchema),
    ui_components: generateUIPrompt(scenarios, menuSchema),
    integration_logic: generateIntegrationPrompt(actions, menuSchema)
  };
  
  return appprompt;
}

// 菜單數據結構分析
function analyzeMenuStructure(menuData) {
  return {
    categories: extractCategories(menuData),
    languages: detectLanguageColumns(menuData),
    priceRange: calculatePriceRange(menuData),
    specialFields: identifySpecialFields(menuData) // 如 size, combo 等
  };
}
```

#### 3.2 模組化 APPprompt 模板

**菜單處理模組：**
```
基於上傳的菜單數據，生成菜單處理邏輯：
- CSV 解析和數據驗證
- 多語言名稱映射建立
- 分類標籤自動識別
- 價格區間分析
- 向量搜尋索引建立
```

**自然語言處理模組：**
```
基於輸入的 BDD 場景和菜單結構，生成處理自然語言輸入的邏輯：
- 意圖識別（點餐、修改、查詢）
- 實體抽取（餐點名稱、數量、修飾詞、尺寸）
- 多語言餐點名稱匹配
- 模糊搜尋和相似度計算
- 上下文理解（指代消解、歷史對話）
```

**查詢模組：**
```
根據 AAprompt 的 Attention 要素和菜單結構，生成資料查詢邏輯：
- RAG 向量搜尋（基於餐點名稱和描述）
- SQL 資料庫查詢（基於分類、價格、可用性）
- 多源資料整合
- 套餐組合查詢
- 相關推薦查詢
```

**回應生成模組：**
```
基於 BDD 場景的預期輸出和菜單數據，生成自然語言回應：
- 確認訊息模板（包含價格、描述）
- 錯誤處理回應（餐點不存在、缺貨等）
- 推薦建議格式（基於分類和價格）
- 套餐說明和選項提示
```

### 第四階段：點餐系統實作

#### 4.1 前端點餐介面

**技術棧：**
- React.js + TypeScript
- 語音識別：Web Speech API
- UI 框架：Material-UI
- 狀態管理：Redux Toolkit

**核心組件：**
```typescript
// 菜單上傳組件
interface MenuUploadProps {
  onFileUpload: (file: File) => Promise<MenuValidationResult>;
  supportedFormats: string[];
  maxFileSize: number;
}

// 菜單預覽組件
interface MenuPreviewProps {
  menuData: MenuData[];
  onEdit: (item: MenuData) => void;
  onDelete: (id: string) => void;
  onBulkEdit: (items: MenuData[]) => void;
}

// 自然語言輸入組件
interface NLInputProps {
  onTextInput: (text: string) => void;
  onVoiceInput: (audio: Blob) => void;
  isListening: boolean;
  menuContext: MenuContext; // 當前菜單上下文
}

// 餐點確認組件
interface OrderConfirmationProps {
  identifiedItems: MenuItem[];
  onConfirm: () => void;
  onModify: (instruction: string) => void;
  menuCategories: MenuCategory[]; // 用於建議替代選項
}

// 購物車組件
interface CartProps {
  items: CartItem[];
  total: number;
  onCheckout: () => void;
}
```

#### 4.2 後端服務架構

**技術棧：**
- Node.js + Express.js
- 資料庫：Firebase Firestore
- AI 服務：OpenAI GPT-4 API
- 語音轉文字：Google Cloud Speech-to-Text

**API 設計：**
```typescript
// 菜單上傳 API
POST /api/menu/upload
Content-Type: multipart/form-data
{
  "file": File, // CSV 檔案
  "restaurant_id": "restaurant123",
  "replace_existing": boolean
}

// 菜單驗證 API
POST /api/menu/validate
{
  "menu_data": MenuData[],
  "validation_rules": ValidationRule[]
}

// 自然語言處理 API
POST /api/nlp/process
{
  "input": "我想點一個大麥克套餐",
  "context": {
    "customer_id": "user123",
    "session_id": "session456",
    "restaurant_id": "restaurant123"
  }
}

// 餐點搜尋 API
GET /api/menu/search
?query=大麥克套餐&restaurant_id=restaurant123&category=套餐&language=zh

// 菜單管理 API
GET /api/menu/list?restaurant_id=restaurant123
PUT /api/menu/item/:id
DELETE /api/menu/item/:id

// 訂單確認 API
POST /api/order/confirm
{
  "items": [...],
  "customer_info": {...},
  "restaurant_id": "restaurant123",
  "payment_method": "..."
}
```

### 第五階段：整合與測試

#### 5.1 系統整合測試

**測試場景：**
1. 菜單上傳 → 數據驗證 → 索引建立 → 搜尋測試
2. BDD 輸入 → AAprompt 輸入 → APPprompt 生成
3. 自然語言點餐流程端到端測試
4. 多語言餐點名稱識別測試
5. 語音輸入準確性測試
6. 餐點識別準確率測試（基於實際菜單數據）
7. 系統性能負載測試
8. 菜單更新和版本管理測試

#### 5.2 用戶體驗測試

**測試內容：**
- GUI 操作直觀性
- 菜單上傳流程順暢度
- APPprompt 生成品質
- 點餐流程順暢度
- 多語言支援效果
- 錯誤處理友善性

### 第六階段：部署與維護

#### 6.1 部署架構

**雲端平台：**
- 前端：Vercel/Netlify
- 後端：Google Cloud Run
- 資料庫：Firebase Firestore
- AI 服務：OpenAI API

**CI/CD 流程：**
- 代碼提交 → 自動測試 → 構建 → 部署
- 多環境管理（開發/測試/生產）

#### 6.2 監控與維護

**監控指標：**
- 系統響應時間
- 自然語言識別準確率
- 用戶滿意度
- 系統錯誤率

## 開發進度狀態

### 已完成功能 ✅

| 階段 | 功能模組 | 完成狀態 | 實現細節 |
|------|----------|----------|----------|
| **第一階段** | **BDD/AAprompt 標準化** | ✅ 完成 | 已建立完整的 BDD 和 AAprompt 模板規範 |
| | 菜單數據結構設計 | ✅ 完成 | 支援 CSV/Excel/JSON 格式，多語言餐點名稱 |
| **第二階段** | **GUI 介面開發** | ✅ 完成 | 完整的 Web 介面，包含所有核心功能 |
| | BDD 編輯器 | ✅ 完成 | CodeMirror 編輯器，語法高亮，即時預覽 |
| | AAprompt 編輯器 | ✅ 完成 | 結構化編輯，模板支援 |
| | 菜單管理介面 | ✅ 完成 | 拖拽上傳，數據驗證，預覽編輯 |
| **第三階段** | **APPprompt 生成引擎** | ✅ 完成 | 整合 Google Gemini AI，智能生成 |
| | 菜單感知生成 | ✅ 完成 | 基於上傳菜單動態生成 APPprompt |
| **第四階段** | **點餐系統實作** | ✅ 完成 | 完整的自然語言點餐流程 |
| | 自然語言處理 | ✅ 完成 | 文字和語音輸入，模糊匹配 |
| | 訂單管理系統 | ✅ 完成 | 訂單確認，修改，總金額計算 |
| | Firebase 整合 | ✅ 完成 | 即時數據同步，訂單歷史 |
| **第五階段** | **整合與測試** | ✅ 完成 | 端到端測試，多語言支援測試 |
| **第六階段** | **部署與維護** | ✅ 完成 | 本地部署，監控機制 |

### 技術實現亮點 🌟

1. **多語言支援**：完整的繁體中文、英文、日文界面
2. **AI 整合**：Google Gemini AI 提供智能點餐體驗
3. **語音識別**：Web Speech API 支援中文語音輸入
4. **模糊匹配**：先進的餐點名稱識別算法
5. **即時同步**：Firebase Firestore 即時數據更新
6. **響應式設計**：支援桌面和移動設備

### 核心功能模組 📋

#### 已實現的主要組件：
- ✅ **菜單管理系統** (`MenuProcessor.ts`, `menu-price-validator.js`)
- ✅ **自然語言處理** (`NLPService.ts`, `GeminiService.ts`)
- ✅ **訂單處理系統** (`OrderService.ts`, `order-confirmation-fix.js`)
- ✅ **語音識別模組** (`speech-recognition.js`)
- ✅ **多語言資源** (`language-resources.js`)
- ✅ **APPprompt 生成器** (`PromptEngine.ts`, `editor.js`)
- ✅ **Firebase 整合** (`FirebaseService.ts`)

#### 技術棧實現：
- **前端**：HTML5, CSS3, JavaScript ES6+, CodeMirror
- **後端**：Node.js, Express.js, TypeScript
- **AI 服務**：Google Generative AI (Gemini)
- **資料庫**：Firebase Firestore
- **語音處理**：Web Speech API

**總開發時間：已完成所有階段**

## 技術風險與對策

### 風險識別
1. **自然語言理解準確性** - 使用多模型集成和持續學習
2. **語音識別噪音干擾** - 實作噪音過濾和確認機制
3. **系統性能瓶頸** - 採用快取策略和負載均衡
4. **AI API 成本控制** - 實作智能快取和批次處理

### 對策方案
- 建立完整的測試數據集
- 實作漸進式功能發布
- 建立監控告警機制
- 準備降級方案

## 系統實際成果 🎯

### 已實現的核心功能

1. **✅ BDD/AAprompt 編輯器**
   - 完整的 CodeMirror 編輯器，支援語法高亮
   - 即時預覽和驗證功能
   - 模板庫和歷史記錄管理
   - 匯入/匯出功能

2. **✅ APPprompt 自動生成**
   - 整合 Google Gemini AI 的智能生成引擎
   - 基於菜單數據的動態 prompt 生成
   - 支援下載和複製生成結果
   - 歷史記錄和版本管理

3. **✅ 智能點餐系統**
   - 自然語言文字輸入處理
   - Web Speech API 語音識別
   - 模糊匹配和精確匹配算法
   - 即時訂單確認和修改
   - 多語言餐點名稱支援

4. **✅ 菜單管理系統**
   - 支援 CSV/Excel/JSON 格式上傳
   - 拖拽式檔案上傳介面
   - 數據驗證和錯誤提示
   - 菜單預覽和編輯功能
   - 分類管理和搜尋功能

### 技術架構優勢

- **🔄 即時同步**：Firebase Firestore 提供即時數據更新
- **🌐 多語言**：完整支援繁體中文、英文、日文
- **🎤 語音識別**：支援中文語音輸入和播報
- **🤖 AI 整合**：Google Gemini AI 提供智能對話
- **📱 響應式**：支援桌面和移動設備
- **🔍 智能搜尋**：模糊匹配和相似度計算

## 系統使用指南 📖

### 快速開始

1. **啟動系統**：
   ```bash
   npm install
   npm start
   ```
   訪問：http://localhost:3005

2. **菜單管理**：
   - 上傳 CSV 格式菜單檔案
   - 系統自動驗證和處理數據
   - 預覽和編輯菜單內容

3. **BDD/AAprompt 編輯**：
   - 使用標準 BDD 語法編寫測試用例
   - 定義 Actor-Action 提示詞結構
   - 即時預覽和驗證

4. **APPprompt 生成**：
   - 輸入 BDD 和 AAprompt 內容
   - 點擊生成按鈕，AI 自動生成 APPprompt
   - 下載或複製生成結果

5. **自然語言點餐**：
   - 文字輸入：「我要一個大麥克套餐」
   - 語音輸入：點擊麥克風按鈕說話
   - 確認訂單並完成購買

### 進階功能

- **多語言切換**：右上角語言選擇器
- **語音控制**：支援語音點餐和確認
- **訂單修改**：可以新增、刪除、修改餐點
- **歷史記錄**：查看過往訂單和生成記錄

## 後續發展方向 🚀

### 短期優化 (1-3個月)

1. **性能優化**
   - 優化 AI 響應速度
   - 改善大檔案上傳處理
   - 增強快取機制

2. **用戶體驗提升**
   - 更直觀的操作流程
   - 增加操作提示和幫助
   - 優化移動端體驗

### 中期擴展 (3-6個月)

1. **功能增強**
   - 支援更多檔案格式 (XML, PDF)
   - 增加圖片識別功能
   - 實現個性化推薦

2. **企業級功能**
   - 多租戶支援
   - 用戶權限管理
   - 數據分析儀表板

### 長期規劃 (6個月以上)

1. **平台化發展**
   - 開放 API 接口
   - 第三方整合支援
   - 插件系統開發

2. **AI 能力擴展**
   - 多模態 AI 整合
   - 更智能的對話系統
   - 預測性分析功能

---

*本開發計畫基於 AI-OS Architecture Agent 方法論制定，旨在創建一個高效、可擴展的自然語言點餐系統開發平台。*
